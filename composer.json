{"name": "hyperf/hyperf-skeleton", "type": "project", "keywords": ["php", "swoole", "framework", "hyperf", "microservice", "middleware"], "description": "A coroutine framework that focuses on hyperspeed and flexible, specifically use for build microservices and middlewares.", "license": "Apache-2.0", "require": {"php": ">=7.4", "ext-bcmath": "*", "ext-zip": "*", "96qbhy/hyperf-auth": "^2.6", "aliyuncs/oss-sdk-php": "^2.7", "doctrine/dbal": "^3.0", "erusev/parsedown": "^1.7", "geshi/geshi": "v1.0.9.1", "hyperf-ext/mail": "^2.2", "hyperf-plus/helper": "2.1.4", "hyperf/async-queue": "~2.2.0", "hyperf/cache": "^2.2", "hyperf/command": "^2.2", "hyperf/config": "~2.2.0", "hyperf/constants": "~2.2.0", "hyperf/crontab": "^2.2", "hyperf/database": "~2.2.0", "hyperf/db-connection": "~2.2.0", "hyperf/elasticsearch": "~2.2.0", "hyperf/framework": "~2.2.0", "hyperf/guzzle": "^2.2", "hyperf/http-server": "~2.2.0", "hyperf/logger": "~2.2.0", "hyperf/memory": "~2.2.0", "hyperf/model-cache": "~2.2.0", "hyperf/paginator": "^2.2", "hyperf/process": "~2.2.0", "hyperf/redis": "~2.2.0", "hyperf/retry": "^2.2", "hyperf/snowflake": "^2.2", "hyperf/swagger": "^2.2", "hyperf/validation": "^2.2", "hyperf/view": "^2.2", "jaeger/querylist": "^4.2", "jaeger/querylist-puppeteer": "^4.0", "jenssegers/agent": "^2.6", "league/html-to-markdown": "^5.1", "michelf/php-markdown": "^1.9", "mpdf/mpdf": "^8.2", "overtrue/pinyin": "^4.0", "phpoffice/phpspreadsheet": "^1.29", "poliander/cron": "^3.0", "symfony/yaml": "^5.4", "yurunsoft/phpmailer-swoole": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/devtool": "~2.2.0", "hyperf/ide-helper": "2.2.*", "hyperf/testing": "~2.2.0", "hyperf/watcher": "^2.2", "mockery/mockery": "^1.0", "phpstan/phpstan": "^0.12", "swoole/ide-helper": "^4.5"}, "suggest": {"ext-openssl": "Required to use HTTPS.", "ext-json": "Required to use JSON.", "ext-pdo": "Required to use MySQL Client.", "ext-pdo_mysql": "Required to use MySQL Client.", "ext-redis": "Required to use Redis Client."}, "autoload": {"psr-4": {"App\\": "app/"}, "files": ["app/Core/Utils/Functions.php"]}, "autoload-dev": {"psr-4": {"HyperfTest\\": "./test/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"optimize-autoloader": true, "sort-packages": true}, "extra": [], "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-autoload-dump": ["rm -rf runtime/container"], "test": "co-phpunit --prepend test/bootstrap.php -c phpunit.xml --colors=always", "cs-fix": "php-cs-fixer fix $1", "analyse": "phpstan analyse --memory-limit 300M -l 0 -c phpstan.neon ./app ./config", "start": ["Composer\\Config::disableProcessTimeout", "php ./bin/hyperf.php start"]}}