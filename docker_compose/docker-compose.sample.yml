version: "3"
services:
  tchip-bi:
    build:
      context: ./composes
    restart: always
    ports:
      - "8057:8057"
    volumes:
      - ../:/var/www
      # - ${PHP_INI_FILE}:/usr/local/etc/php/php.ini
      # - ${PHP_WWW_CONF_FILE}:/usr/local/etc/php-fpm.d/www.conf
#      - ${PHP_LOG_DIR}:/var/log/php
#    networks:
#      - backend
    container_name: ${APP_ENV}-${TCHIP_BI_CONTAINER_NAME}
    entrypoint: bash /var/www/start.sh
    logging:
      options:
        max-size: "200m"