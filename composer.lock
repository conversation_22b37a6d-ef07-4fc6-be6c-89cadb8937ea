{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "f46c85d6c6d2948bc30ea2952e8c1aaa", "packages": [{"name": "96qbhy/hyperf-auth", "version": "v2.7.1", "source": {"type": "git", "url": "https://github.com/qbhy/hyperf-auth.git", "reference": "93bee9f34c4329c019a3b297c1a2a8cedca5a17f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qbhy/hyperf-auth/zipball/93bee9f34c4329c019a3b297c1a2a8cedca5a17f", "reference": "93bee9f34c4329c019a3b297c1a2a8cedca5a17f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"96qbhy/simple-jwt": "^v1.4.0", "ext-json": "*", "ext-redis": "*", "ext-swoole": ">=4.4", "hyperf/cache": "^2.0", "hyperf/di": "^2.0", "php": ">=7.2"}, "require-dev": {"brainmaestro/composer-git-hooks": "^2.8", "friendsofphp/php-cs-fixer": "^2.14", "hyperf/command": "^2.2", "hyperf/config": "^2.0", "hyperf/database": "^2.0", "hyperf/event": "^2.2", "hyperf/framework": "^2.0", "hyperf/redis": "^2.0", "hyperf/session": "^2.0", "hyperf/testing": "^2.0", "hyperf/utils": "^2.2", "itsgoingd/clockwork": "^5.0", "phpstan/phpstan": "^0.12", "swoft/swoole-ide-helper": "dev-master", "symfony/console": "^5.3", "symfony/var-dumper": "^5.3"}, "suggest": {"ext-redis": "*"}, "type": "library", "extra": {"hyperf": {"config": "Qbhy\\HyperfAuth\\ConfigProvider"}, "hooks": {"pre-commit": ["echo committing as $(git config user.name)", "vendor/bin/php-cs-fixer fix .", "git add .", "composer test"]}}, "autoload": {"files": ["src/helper.php"], "psr-4": {"Qbhy\\HyperfAuth\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "hyperf 的 auth 组件", "keywords": ["hyperf", "hyperf-auth", "jwt-auth", "php"], "support": {"issues": "https://github.com/qbhy/hyperf-auth/issues", "source": "https://github.com/qbhy/hyperf-auth/tree/v2.7.1"}, "time": "2022-06-16T12:16:22+00:00"}, {"name": "96qbhy/simple-jwt", "version": "v1.4", "source": {"type": "git", "url": "https://github.com/qbhy/simple-jwt.git", "reference": "60e7b5a86086b29469e5cd1501145336fdc7c3c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qbhy/simple-jwt/zipball/60e7b5a86086b29469e5cd1501145336fdc7c3c7", "reference": "60e7b5a86086b29469e5cd1501145336fdc7c3c7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/cache": "^1.10", "ext-json": ">=1.0", "php": ">=7.1"}, "require-dev": {"brainmaestro/composer-git-hooks": "^2.8", "friendsofphp/php-cs-fixer": "^2.16", "hyperf/utils": "^1.1", "phpunit/phpunit": "^9.1"}, "type": "library", "extra": {"hooks": {"pre-commit": ["echo committing as $(git config user.name)", "vendor/bin/php-cs-fixer fix .", "git add .", "composer test"]}, "hyperf": {"config": "Qbhy\\SimpleJwt\\Hyperf\\ConfigProvider"}, "laravel": {"providers": ["Qbhy\\SimpleJwt\\Laravel\\ServiceProvider"]}}, "autoload": {"psr-4": {"Qbhy\\SimpleJwt\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "96qbhy", "email": "<EMAIL>"}], "description": "简单的 jwt 实现", "support": {"issues": "https://github.com/qbhy/simple-jwt/issues", "source": "https://github.com/qbhy/simple-jwt/tree/v1.4"}, "time": "2021-09-29T03:15:34+00:00"}, {"name": "aliyuncs/oss-sdk-php", "version": "v2.7.2", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "483dd0b8bff5d47f0e4ffc99f6077a295c5ccbb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-oss-php-sdk/zipball/483dd0b8bff5d47f0e4ffc99f6077a295c5ccbb5", "reference": "483dd0b8bff5d47f0e4ffc99f6077a295c5ccbb5", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"php-coveralls/php-coveralls": "*", "phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "support": {"issues": "https://github.com/aliyun/aliyun-oss-php-sdk/issues", "source": "https://github.com/aliyun/aliyun-oss-php-sdk/tree/v2.7.2"}, "time": "2024-10-28T10:41:12+00:00"}, {"name": "cache/adapter-common", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/php-cache/adapter-common.git", "reference": "8788309be72aa7be69b88cdc0687549c74a7d479"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-cache/adapter-common/zipball/8788309be72aa7be69b88cdc0687549c74a7d479", "reference": "8788309be72aa7be69b88cdc0687549c74a7d479", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"cache/tag-interop": "^1.0", "php": ">=7.4", "psr/cache": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "psr/simple-cache": "^1.0"}, "require-dev": {"cache/integration-tests": "^0.17", "phpunit/phpunit": "^7.5.20 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Cache\\Adapter\\Common\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/aequasi"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyholm"}], "description": "Common classes for PSR-6 adapters", "homepage": "http://www.php-cache.com/en/latest/", "keywords": ["cache", "psr-6", "tag"], "support": {"source": "https://github.com/php-cache/adapter-common/tree/1.3.0"}, "time": "2022-01-15T15:47:19+00:00"}, {"name": "cache/filesystem-adapter", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/php-cache/filesystem-adapter.git", "reference": "f1faaae40aaa696ef899cef6f6888aedb90b419b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-cache/filesystem-adapter/zipball/f1faaae40aaa696ef899cef6f6888aedb90b419b", "reference": "f1faaae40aaa696ef899cef6f6888aedb90b419b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"cache/adapter-common": "^1.0", "league/flysystem": "^1.0", "php": ">=7.4", "psr/cache": "^1.0 || ^2.0", "psr/simple-cache": "^1.0"}, "provide": {"psr/cache-implementation": "^1.0", "psr/simple-cache-implementation": "^1.0"}, "require-dev": {"cache/integration-tests": "^0.17", "phpunit/phpunit": "^7.5.20 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Cache\\Adapter\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/aequasi"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyholm"}], "description": "A PSR-6 cache implementation using filesystem. This implementation supports tags", "homepage": "http://www.php-cache.com/en/latest/", "keywords": ["cache", "filesystem", "psr-6", "tag"], "support": {"source": "https://github.com/php-cache/filesystem-adapter/tree/1.2.0"}, "time": "2022-01-15T15:47:19+00:00"}, {"name": "cache/tag-interop", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-cache/tag-interop.git", "reference": "b062b1d735357da50edf8387f7a8696f3027d328"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-cache/tag-interop/zipball/b062b1d735357da50edf8387f7a8696f3027d328", "reference": "b062b1d735357da50edf8387f7a8696f3027d328", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.5 || ^7.0 || ^8.0", "psr/cache": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Cache\\TagInterop\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nicolas-grekas"}], "description": "Framework interoperable interfaces for tags", "homepage": "https://www.php-cache.com/en/latest/", "keywords": ["cache", "psr", "psr6", "tag"], "support": {"issues": "https://github.com/php-cache/tag-interop/issues", "source": "https://github.com/php-cache/tag-interop/tree/1.1.0"}, "time": "2021-12-31T10:03:23+00:00"}, {"name": "clue/socket-raw", "version": "v1.6.0", "source": {"type": "git", "url": "https://github.com/clue/socket-raw.git", "reference": "91e9f619f6769f931454a9882c21ffd7623d06cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/socket-raw/zipball/91e9f619f6769f931454a9882c21ffd7623d06cb", "reference": "91e9f619f6769f931454a9882c21ffd7623d06cb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-sockets": "*", "php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.35"}, "type": "library", "autoload": {"psr-4": {"Socket\\Raw\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Simple and lightweight OOP wrapper for PHP's low-level sockets extension (ext-sockets).", "homepage": "https://github.com/clue/socket-raw", "keywords": ["Socket", "client", "datagram", "dgram", "icmp", "ipv6", "server", "stream", "tcp", "udg", "udp", "unix"], "support": {"issues": "https://github.com/clue/socket-raw/issues", "source": "https://github.com/clue/socket-raw/tree/v1.6.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-04-14T14:58:06+00:00"}, {"name": "doctrine/annotations", "version": "1.13.3", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "648b0343343565c4a056bfc8392201385e8d89f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/648b0343343565c4a056bfc8392201385e8d89f0", "reference": "648b0343343565c4a056bfc8392201385e8d89f0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.1.5", "symfony/cache": "^4.4 || ^5.2", "vimeo/psalm": "^4.10"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.13.3"}, "time": "2022-07-02T10:48:51+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.13.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/dbal", "version": "3.6.1", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "57815c7bbcda3cd18871d253c1dd8cbe56f8526e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/57815c7bbcda3cd18871d253c1dd8cbe56f8526e", "reference": "57815c7bbcda3cd18871d253c1dd8cbe56f8526e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.11|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1|^2", "php": "^7.4 || ^8.0", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "11.1.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2022.3", "phpstan/phpstan": "1.10.3", "phpstan/phpstan-strict-rules": "^1.5", "phpunit/phpunit": "9.6.4", "psalm/plugin-phpunit": "0.18.4", "squizlabs/php_codesniffer": "3.7.2", "symfony/cache": "^5.4|^6.0", "symfony/console": "^4.4|^5.4|^6.0", "vimeo/psalm": "4.30.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/3.6.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2023-03-02T19:26:24+00:00"}, {"name": "doctrine/deprecations", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1|^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5|^8.5|^9.5", "psr/log": "^1|^2|^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v1.0.0"}, "time": "2022-05-02T15:47:09+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/41370af6a30faa9dc0368c4a6814d596e81aba7f", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2020-05-29T18:28:51+00:00"}, {"name": "doctrine/inflector", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "8b7ff3e4b7de6b2c84da85637b59fd2880ecaa89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/8b7ff3e4b7de6b2c84da85637b59fd2880ecaa89", "reference": "8b7ff3e4b7de6b2c84da85637b59fd2880ecaa89", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^4.10"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-10-22T20:16:43+00:00"}, {"name": "doctrine/instantiator", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/10dcfce151b967d20fde1b34ae6640712c3891bc", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-03-03T08:28:38+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "egulias/email-validator", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "f88dcf4b14af14a98ad96b14b2b317969eab6715"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/f88dcf4b14af14a98ad96b14b2b317969eab6715", "reference": "f88dcf4b14af14a98ad96b14b2b317969eab6715", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/lexer": "^1.2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.1"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2022-06-18T20:57:19+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v7.17.1", "source": {"type": "git", "url": "**************:elastic/elasticsearch-php.git", "reference": "f1b8918f411b837ce5f6325e829a73518fd50367"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/f1b8918f411b837ce5f6325e829a73518fd50367", "reference": "f1b8918f411b837ce5f6325e829a73518fd50367", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0", "LGPL-2.1-only"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "time": "2022-09-30T12:28:55+00:00"}, {"name": "erusev/parsedown", "version": "1.7.4", "source": {"type": "git", "url": "https://github.com/erusev/parsedown.git", "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/erusev/parsedown/zipball/cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mbstring": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35"}, "type": "library", "autoload": {"psr-0": {"Parsedown": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://erusev.com"}], "description": "<PERSON><PERSON>r for <PERSON>down.", "homepage": "http://parsedown.org", "keywords": ["markdown", "parser"], "support": {"issues": "https://github.com/erusev/parsedown/issues", "source": "https://github.com/erusev/parsedown/tree/1.7.x"}, "time": "2019-12-30T22:54:17+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "abe3791d231167f14eb80d413420d1eab91163a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/abe3791d231167f14eb80d413420d1eab91163a8", "reference": "abe3791d231167f14eb80d413420d1eab91163a8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"source": "https://github.com/ezimuel/guzzlestreams/tree/3.0.1"}, "time": "2020-02-14T23:11:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "92b8161404ab1ad84059ebed41d9f757e897ce74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/92b8161404ab1ad84059ebed41d9f757e897ce74", "reference": "92b8161404ab1ad84059ebed41d9f757e897ce74", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "replace": {"guzzlehttp/ringphp": "self.version"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~9.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "support": {"source": "https://github.com/ezimuel/ringphp/tree/1.2.0"}, "time": "2021-11-16T11:51:30+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.16.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "523407fb06eb9e5f3d59889b3978d5bfe94299c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/523407fb06eb9e5f3d59889b3978d5bfe94299c8", "reference": "523407fb06eb9e5f3d59889b3978d5bfe94299c8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.16.0"}, "time": "2022-09-18T07:06:19+00:00"}, {"name": "fig/http-message-util", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/php-fig/http-message-util.git", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message-util/zipball/9d94dc0154230ac39e5bf89398b324a86f63f765", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "suggest": {"psr/http-message": "The package containing the PSR-7 interfaces"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Fig\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Utility classes and constants for use with PSR-7 (psr/http-message)", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-message-util/issues", "source": "https://github.com/php-fig/http-message-util/tree/1.1.5"}, "time": "2020-11-24T22:02:12+00:00"}, {"name": "geshi/geshi", "version": "v1.0.9.1", "source": {"type": "git", "url": "https://github.com/GeSHi/geshi-1.0.git", "reference": "fd22ab78481bf90337862b590e6f7517863926b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GeSHi/geshi-1.0/zipball/fd22ab78481bf90337862b590e6f7517863926b8", "reference": "fd22ab78481bf90337862b590e6f7517863926b8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.5 || ^7.5 || ^8.2"}, "type": "library", "autoload": {"classmap": ["src/geshi/", "src/geshi.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://blog.benny-baumann.de/", "role": "Developer"}], "description": "Generic Syntax Highlighter", "homepage": "http://qbnz.com/highlighter/", "support": {"forum": "https://lists.sourceforge.net/lists/listinfo/geshi-users", "irc": "irc://irc.freenode.org/geshi", "issues": "https://sourceforge.net/p/geshi/feature-requests/", "source": "https://github.com/GeSHi/geshi-1.0/tree/v1.0.9.1"}, "time": "2019-10-20T20:54:46+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "0690bde05318336c7221785f2a932467f98b64ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/0690bde05318336c7221785f2a932467f98b64ca", "reference": "0690bde05318336c7221785f2a932467f98b64ca", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0 || ^8.0", "phpoption/phpoption": "^1.8"}, "require-dev": {"phpunit/phpunit": "^6.5.14 || ^7.5.20 || ^8.5.19 || ^9.5.8"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.0.4"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2021-11-21T21:41:47+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.5.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "b50a2a1251152e43f6a37f0fa053e730a67d25ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/b50a2a1251152e43f6a37f0fa053e730a67d25ba", "reference": "b50a2a1251152e43f6a37f0fa053e730a67d25ba", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.9 || ^2.4", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.29 || ^9.5.23", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "7.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.5.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-08-28T15:39:27+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "b94b2807d85443f9719887892882d0329d1e2598"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/b94b2807d85443f9719887892882d0329d1e2598", "reference": "b94b2807d85443f9719887892882d0329d1e2598", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2022-08-28T14:55:35+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.4.3", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "67c26b443f348a51926030c83481b85718457d3d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/67c26b443f348a51926030c83481b85718457d3d", "reference": "67c26b443f348a51926030c83481b85718457d3d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.4.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2022-10-26T14:07:24+00:00"}, {"name": "hyperf-ext/contract", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/hyperf-ext/contract.git", "reference": "8323bc9175ef48a4efe0a9ed8c028bc48e5f9353"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf-ext/contract/zipball/8323bc9175ef48a4efe0a9ed8c028bc48e5f9353", "reference": "8323bc9175ef48a4efe0a9ed8c028bc48e5f9353", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0"}, "type": "library", "autoload": {"psr-4": {"HyperfExt\\Contract\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The contracts of HyperfExt.", "keywords": ["hyperf", "php"], "support": {"issues": "https://github.com/hyperf-ext/contract/issues", "source": "https://github.com/hyperf-ext/contract/tree/v2.2.0"}, "time": "2021-07-30T18:01:36+00:00"}, {"name": "hyperf-ext/mail", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/hyperf-ext/mail.git", "reference": "21fc22628d03d926226d6bca28bc0be5d8669403"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf-ext/mail/zipball/21fc22628d03d926226d6bca28bc0be5d8669403", "reference": "21fc22628d03d926226d6bca28bc0be5d8669403", "shasum": ""}, "require": {"ext-json": "*", "ext-swoole": ">=4.5", "hyperf-ext/contract": "~2.2.0", "hyperf/async-queue": "~2.2.0", "hyperf/command": "~2.2.0", "hyperf/config": "~2.2.0", "hyperf/devtool": "~2.2.0", "hyperf/di": "~2.2.0", "hyperf/event": "~2.2.0", "hyperf/filesystem": "~2.2.0", "hyperf/framework": "~2.2.0", "hyperf/translation": "~2.2.0", "hyperf/view": "~2.2.0", "php": ">=7.3", "swiftmailer/swiftmailer": "^6.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/testing": "~2.2.0", "mockery/mockery": "^1.0", "phpstan/phpstan": "^0.12", "swoole/ide-helper": "dev-master"}, "suggest": {"alibabacloud/dm": "Required to use the Aliyun (Alibaba Cloud) DM mail driver (^1.8).", "aws/aws-sdk-php": "Required to use the AWS SES mail driver (^3.0).", "hyperf/guzzle": "Required to use the mail driver (^2.0).", "wildbit/swiftmailer-postmark": "Required to use Postmark mail driver (^3.3)."}, "type": "library", "extra": {"hyperf": {"config": "HyperfExt\\Mail\\ConfigProvider"}}, "autoload": {"psr-4": {"HyperfExt\\Mail\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Hyperf Mail package.", "keywords": ["hyperf", "mail", "php"], "support": {"issues": "https://github.com/hyperf-ext/mail/issues", "source": "https://github.com/hyperf-ext/mail/tree/v2.2.0"}, "time": "2021-07-30T18:26:27+00:00"}, {"name": "hyperf-plus/helper", "version": "v2.1.4", "source": {"type": "git", "url": "https://github.com/hyperf-plus/helper.git", "reference": "233db4ec5cddfd3c609391bcbb639e50f99aec34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf-plus/helper/zipball/233db4ec5cddfd3c609391bcbb639e50f99aec34", "reference": "233db4ec5cddfd3c609391bcbb639e50f99aec34", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"nette/php-generator": "^3.3", "php": ">=7.3"}, "require-dev": {"hyperf/devtool": "^2.0.0"}, "suggest": {"hyperf/command": "*", "hyperf/di": "*"}, "type": "library", "extra": {"hyperf": {"config": "HPlus\\Helper\\ConfigProvider"}}, "autoload": {"files": ["./src/Helper/helper.php"], "psr-4": {"HPlus\\Helper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A helper component for hyperf.", "keywords": ["helper", "hyperf", "php"], "support": {"issues": "https://github.com/hyperf-plus/helper/issues", "source": "https://github.com/hyperf-plus/helper/tree/v2.1.4"}, "time": "2022-02-18T12:47:13+00:00"}, {"name": "hyperf/async-queue", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/async-queue.git", "reference": "6e8da55c8b19322023d5fdd970bcbddf774e79f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/async-queue/zipball/6e8da55c8b19322023d5fdd970bcbddf774e79f0", "reference": "6e8da55c8b19322023d5fdd970bcbddf774e79f0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/command": "~2.2.0", "hyperf/contract": "~2.2.8", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0", "psr/event-dispatcher": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Required to dispatch a event.", "hyperf/logger": "Required to use QueueHandleListener.", "hyperf/process": "Auto register the consumer process for server."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\AsyncQueue\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\AsyncQueue\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A async queue component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["async-queue", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/cache", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/cache.git", "reference": "5d133778db7b38bd40be8ef970dcb2d75a5b68ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/cache/zipball/5d133778db7b38bd40be8ef970dcb2d75a5b68ed", "reference": "5d133778db7b38bd40be8ef970dcb2d75a5b68ed", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0", "psr/simple-cache": "^1.0"}, "suggest": {"hyperf/di": "Use cache annotations.", "hyperf/event": "Use listener to delete annotation cache."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Cache\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A cache component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["cache", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/command", "version": "v2.2.35", "source": {"type": "git", "url": "https://github.com/hyperf/command.git", "reference": "48cd9789166ecf5f3009de45bcf128b997ec5f88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/command/zipball/48cd9789166ecf5f3009de45bcf128b997ec5f88", "reference": "48cd9789166ecf5f3009de45bcf128b997ec5f88", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/event-dispatcher": "^1.0", "symfony/console": ">=5.0 <5.4.12"}, "suggest": {"hyperf/di": "Required to use annotations."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Hyperf\\Command\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Command for hyperf", "keywords": ["command", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/command/issues", "source": "https://github.com/hyperf/command/tree/v2.2.35"}, "time": "2022-08-27T07:28:56+00:00"}, {"name": "hyperf/config", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/config.git", "reference": "9437f45aea36358840c907691e9b28492550e9a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/config/zipball/9437f45aea36358840c907691e9b28492550e9a3", "reference": "9437f45aea36358840c907691e9b28492550e9a3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0", "symfony/finder": "^5.0"}, "suggest": {"hyperf/di": "Allows using @Value annotation", "hyperf/event": "Allows using @Value annotation", "hyperf/framework": "Allows using @Value annotation", "vlucas/phpdotenv": "Allows using enviroment value to override the config"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Config\\ConfigProvider"}}, "autoload": {"files": ["./src/Functions.php"], "psr-4": {"Hyperf\\Config\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "An independent component that provides configuration container.", "homepage": "https://hyperf.io", "keywords": ["config", "configuration", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/constants", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/constants.git", "reference": "adb652d446bde384af9ad907cfabe900f3dd08b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/constants/zipball/adb652d446bde384af9ad907cfabe900f3dd08b8", "reference": "adb652d446bde384af9ad907cfabe900f3dd08b8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/di": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2"}, "suggest": {"hyperf/translation": "Required to use translation."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Constants\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Constants\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A constants component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["constants", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/context", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/context.git", "reference": "f2e77442693a07d47f7ca97f8b2fdb8e17196a47"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/context/zipball/f2e77442693a07d47f7ca97f8b2fdb8e17196a47", "reference": "f2e77442693a07d47f7ca97f8b2fdb8e17196a47", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/engine": "^1.1", "php": ">=7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Hyperf\\Context\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A coroutine context library.", "homepage": "https://hyperf.io", "keywords": ["Context", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/contract", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/contract.git", "reference": "1ebf037c91d76ec05af9e5cb3335b0f5ec810e27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/contract/zipball/1ebf037c91d76ec05af9e5cb3335b0f5ec810e27", "reference": "1ebf037c91d76ec05af9e5cb3335b0f5ec810e27", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Hyperf\\Contract\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "The contracts of Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/crontab", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/crontab.git", "reference": "64e1a6db961fdf46a111ec172956abb5a386511c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/crontab/zipball/64e1a6db961fdf46a111ec172956abb5a386511c", "reference": "64e1a6db961fdf46a111ec172956abb5a386511c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/utils": "~2.2.0", "nesbot/carbon": "^2.0", "php": ">=7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Crontab\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Crontab\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A crontab component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["crontab", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/database", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/database.git", "reference": "e3c879f238fd92d79aef3196515aa7894aeff5a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/database/zipball/e3c879f238fd92d79aef3196515aa7894aeff5a0", "reference": "e3c879f238fd92d79aef3196515aa7894aeff5a0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/macroable": "~2.2.0", "hyperf/utils": "~2.2.8", "nesbot/carbon": "^2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0", "psr/event-dispatcher": "^1.0"}, "suggest": {"doctrine/dbal": "Required to rename columns (^3.0).", "nikic/php-parser": "Required to use ModelCommand. (^4.0)", "php-di/phpdoc-reader": "Required to use ModelCommand. (^2.2)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Hyperf\\Database\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A flexible database library.", "homepage": "https://hyperf.io", "keywords": ["database", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/db-connection", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/db-connection.git", "reference": "05d2a1e858f01682739e066430b339a9bcdef837"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/db-connection/zipball/05d2a1e858f01682739e066430b339a9bcdef837", "reference": "05d2a1e858f01682739e066430b339a9bcdef837", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/database": "~2.2.0", "hyperf/di": "~2.2.0", "hyperf/framework": "~2.2.0", "hyperf/model-listener": "~2.2.0", "hyperf/pool": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\DbConnection\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\DbConnection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A hyperf db connection handler for hyperf/database.", "homepage": "https://hyperf.io", "keywords": ["Connection", "database", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/devtool", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/devtool.git", "reference": "f447311bf7507ff2b13658250ef3bcff7a9bc881"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/devtool/zipball/f447311bf7507ff2b13658250ef3bcff7a9bc881", "reference": "f447311bf7507ff2b13658250ef3bcff7a9bc881", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/command": "~2.2.0", "hyperf/contract": "~2.2.0", "hyperf/di": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Devtool\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Devtool\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A Devtool for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["devtool", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/di", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/di.git", "reference": "471c3cf9e0c02ab3aaa0c6e9884062bd541e4577"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/di/zipball/471c3cf9e0c02ab3aaa0c6e9884062bd541e4577", "reference": "471c3cf9e0c02ab3aaa0c6e9884062bd541e4577", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/annotations": "^1.6", "doctrine/instantiator": "^1.0", "nikic/php-parser": "^4.1", "php": ">=7.3", "php-di/phpdoc-reader": "^2.2", "psr/container": "^1.0|^2.0", "symfony/finder": "^5.0", "vlucas/phpdotenv": "^5.0"}, "suggest": {"ext-pcntl": "Required to scan annotations.", "hyperf/config": "Require this component for annotation scan progress to retrieve the scan path."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Di\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Di\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A DI for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["annotation", "di", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/dispatcher", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/dispatcher.git", "reference": "bcffa8faa11367204c4e00e93f6425d600603e03"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/dispatcher/zipball/bcffa8faa11367204c4e00e93f6425d600603e03", "reference": "bcffa8faa11367204c4e00e93f6425d600603e03", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0", "psr/http-message": "^1.0", "psr/http-server-middleware": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Dispatcher\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Dispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A HTTP Server for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["dispatcher", "filter", "hyperf", "middleware", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/elasticsearch", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/elasticsearch.git", "reference": "ee6ce4771aac0094f540e1bb3bcf23bfea24ac37"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/elasticsearch/zipball/ee6ce4771aac0094f540e1bb3bcf23bfea24ac37", "reference": "ee6ce4771aac0094f540e1bb3bcf23bfea24ac37", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"elasticsearch/elasticsearch": "^7.0", "hyperf/guzzle": "~2.2.0", "php": ">=7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Hyperf\\Elasticsearch\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Elasticsearch client for hyperf", "keywords": ["elasticsearch", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/elasticsearch/issues", "source": "https://github.com/hyperf/elasticsearch/tree/v2.2.33"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/engine", "version": "v1.2.2", "source": {"type": "git", "url": "https://github.com/hyperf/engine.git", "reference": "12fdb2b4cec9ee92fc808321a7378cc6251e52cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/engine/zipball/12fdb2b4cec9ee92fc808321a7378cc6251e52cd", "reference": "12fdb2b4cec9ee92fc808321a7378cc6251e52cd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/guzzle": "^2.2", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^9.4", "swoole/ide-helper": "dev-master"}, "suggest": {"ext-swoole": ">=4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Hyperf\\Engine\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "keywords": ["hyperf", "php"], "support": {"issues": "https://github.com/hyperf/engine/issues", "source": "https://github.com/hyperf/engine/tree/v1.2.2"}, "time": "2022-08-06T05:25:43+00:00"}, {"name": "hyperf/event", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/event.git", "reference": "cd92f5c1218c65f29b15c4d12dcf5835e0426ac8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/event/zipball/cd92f5c1218c65f29b15c4d12dcf5835e0426ac8", "reference": "cd92f5c1218c65f29b15c4d12dcf5835e0426ac8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "php": ">=7.2", "psr/event-dispatcher": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotatioins."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Event\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Event\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "an event manager that implements PSR-14.", "homepage": "https://hyperf.io", "keywords": ["event", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/exception-handler", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/exception-handler.git", "reference": "4ec8f6debf530b661bb2b436ef96da53ce0abbdc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/exception-handler/zipball/4ec8f6debf530b661bb2b436ef96da53ce0abbdc", "reference": "4ec8f6debf530b661bb2b436ef96da53ce0abbdc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/dispatcher": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\ExceptionHandler\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\ExceptionHandler\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Exception handler for hyperf", "homepage": "https://hyperf.io", "keywords": ["exception-handler", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/filesystem", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/filesystem.git", "reference": "58fe056bf50f732482fe4bd8946a8e3b9a1640d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/filesystem/zipball/58fe056bf50f732482fe4bd8946a8e3b9a1640d3", "reference": "58fe056bf50f732482fe4bd8946a8e3b9a1640d3", "shasum": ""}, "require": {"hyperf/di": "~2.2.0", "league/flysystem": "^1.0|^2.0", "php": ">=7.2"}, "suggest": {"ext-swoole": "Required to use swoole AliyunOssHook.", "hyperf/flysystem-oss": "Required to use aliyun oss adapter when use `league/flysystem` v2.0", "hyperf/guzzle": "required to use s3 adapter", "league/flysystem-aws-s3-v3": "required to use s3 adapter", "league/flysystem-memory": "required to use memory adapter", "overtrue/flysystem-cos": "Required to use cos adapter (^3.0|^4.0)", "overtrue/flysystem-qiniu": "Required to use qiniu adapter (^1.0|^2.0)", "xxtime/flysystem-aliyun-oss": "Required to use aliyun oss adapter when use `league/flysystem` v1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Filesystem\\ConfigProvider"}}, "autoload": {"files": ["src/Adapter/AliyunOssHook.php"], "psr-4": {"Hyperf\\Filesystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "flysystem integration for hyperf", "keywords": ["hyperf", "php"], "support": {"issues": "https://github.com/hyperf/filesystem/issues", "source": "https://github.com/hyperf/filesystem/tree/v2.2.33"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/framework", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/framework.git", "reference": "49e98fb747214bcac79aecadda8349e70b8f2891"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/framework/zipball/49e98fb747214bcac79aecadda8349e70b8f2891", "reference": "49e98fb747214bcac79aecadda8349e70b8f2891", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"fig/http-message-util": "^1.1.2", "hyperf/contract": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0", "psr/event-dispatcher": "^1.0", "psr/log": "^1.0|^2.0|^3.0"}, "suggest": {"ext-swoole": "Required to use swoole engine.", "hyperf/command": "Required to use Command annotation.", "hyperf/di": "Required to use Command annotation.", "hyperf/dispatcher": "Required to use BootApplication event.", "symfony/event-dispatcher": "Required to use symfony event dispatcher (^5.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Framework\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Framework\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A coroutine framework that focuses on hyperspeed and flexible, specifically use for build microservices and middlewares.", "homepage": "https://hyperf.io", "keywords": ["Microservice", "framework", "hyperf", "middleware", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/guzzle", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/guzzle.git", "reference": "ef4d6b02778cf44b7f0edefd9c33a5d8d08ad48f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/guzzle/zipball/ef4d6b02778cf44b7f0edefd9c33a5d8d08ad48f", "reference": "ef4d6b02778cf44b7f0edefd9c33a5d8d08ad48f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^6.3|^7.0", "hyperf/utils": "~2.2.0", "php": ">=7.0", "psr/container": "^1.0|^2.0", "psr/http-message": "^1.0"}, "suggest": {"hyperf/pool": "Required to use pool handler."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Guzzle\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Swoole coroutine handler for guzzle", "keywords": ["Guzzle", "handler", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/guzzle/issues", "source": "https://github.com/hyperf/guzzle/tree/v2.2.33"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/http-message", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/http-message.git", "reference": "d71d796177ec72fe30d116c57ed8bd8121a57b6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/http-message/zipball/d71d796177ec72fe30d116c57ed8bd8121a57b6e", "reference": "d71d796177ec72fe30d116c57ed8bd8121a57b6e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/utils": "~2.2.0", "laminas/laminas-mime": "^2.7", "psr/http-message": "^1.0"}, "suggest": {"psr/container": "Required to replace RequestParserInterface."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\HttpMessage\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\HttpMessage\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "microservice framework base on swoole", "keywords": ["http-message", "hyperf", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/http-message/issues", "source": "https://github.com/hyperf/http-message/tree/v2.2.33"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/http-server", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/http-server.git", "reference": "0f1f22d408de82c9f8fb3f2cf57457e319210798"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/http-server/zipball/0f1f22d408de82c9f8fb3f2cf57457e319210798", "reference": "0f1f22d408de82c9f8fb3f2cf57457e319210798", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/dispatcher": "~2.2.0", "hyperf/event": "~2.2.0", "hyperf/exception-handler": "~2.2.0", "hyperf/http-message": "~2.2.0", "hyperf/macroable": "~2.2.0", "hyperf/server": "~2.2.0", "hyperf/utils": "~2.2.0", "nikic/fast-route": "^1.3", "php": ">=7.2", "psr/container": "^1.0|^2.0"}, "suggest": {"hyperf/di": "Required to use annotations."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\HttpServer\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\HttpServer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A HTTP Server for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["http", "http-server", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/logger", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/logger.git", "reference": "f19929efaeca4aba2b91e7452f4f1b3245bfac51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/logger/zipball/f19929efaeca4aba2b91e7452f4f1b3245bfac51", "reference": "f19929efaeca4aba2b91e7452f4f1b3245bfac51", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/utils": "~2.2.0", "monolog/monolog": "^2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0", "psr/log": "^1.0|^2.0|^3.0"}, "conflict": {"monolog/monolog": ">=2.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Logger\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Logger\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A logger component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "logger", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/macroable", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/macroable.git", "reference": "e4e1b3ed614a5a9d4c24a48b0b3a554eb712af9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/macroable/zipball/e4e1b3ed614a5a9d4c24a48b0b3a554eb712af9f", "reference": "e4e1b3ed614a5a9d4c24a48b0b3a554eb712af9f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Hyperf\\Macroable\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/macroable", "homepage": "https://hyperf.io", "keywords": ["hyperf", "macroable", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/memory", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/memory.git", "reference": "8adfac46a0f52385a4ad2e8bc4f956c34cb6f25e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/memory/zipball/8adfac46a0f52385a4ad2e8bc4f956c34cb6f25e", "reference": "8adfac46a0f52385a4ad2e8bc4f956c34cb6f25e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Memory\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Memory\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "An independent component that use to operate and manage memory.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "memory", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/model-cache", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/model-cache.git", "reference": "f1e4148da7f6dc4b5eae74594311e53674fd532d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/model-cache/zipball/f1e4148da7f6dc4b5eae74594311e53674fd532d", "reference": "f1e4148da7f6dc4b5eae74594311e53674fd532d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/db-connection": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0", "psr/simple-cache": "^1.0"}, "suggest": {"hyperf/event": "Required to use DeleteCacheListener."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\ModelCache\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\ModelCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A model cache component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "model-cache", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/model-listener", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/model-listener.git", "reference": "0ac64b517db0d9e192650cd70f81a2962cd54e63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/model-listener/zipball/0ac64b517db0d9e192650cd70f81a2962cd54e63", "reference": "0ac64b517db0d9e192650cd70f81a2962cd54e63", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/database": "~2.2.0", "hyperf/di": "~2.2.0", "hyperf/event": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\ModelListener\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\ModelListener\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A model listener for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "model-listener", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/paginator", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/paginator.git", "reference": "dc9429ecbd579ef54fa81be9c1de149248f87d0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/paginator/zipball/dc9429ecbd579ef54fa81be9c1de149248f87d0a", "reference": "dc9429ecbd579ef54fa81be9c1de149248f87d0a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2"}, "suggest": {"hyperf/event": "Reqiured to use PageResolverListener.", "hyperf/framework": "Reqiured to use PageResolverListener.", "hyperf/http-server": "Reqiured to use PageResolverListener."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Paginator\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Paginator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A paginator component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "paginator", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/pool", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/pool.git", "reference": "b66965453f404f09570a166e9c103a074d2dbd4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/pool/zipball/b66965453f404f09570a166e9c103a074d2dbd4e", "reference": "b66965453f404f09570a166e9c103a074d2dbd4e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Pool\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Pool\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "An independent universal connection pool component.", "homepage": "https://hyperf.io", "keywords": ["connection-pool", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/process", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/process.git", "reference": "a93b2a4bf0455e3f639649046705e7b3b6ccd137"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/process/zipball/a93b2a4bf0455e3f639649046705e7b3b6ccd137", "reference": "a93b2a4bf0455e3f639649046705e7b3b6ccd137", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0", "psr/event-dispatcher": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Required to dump the message before and after process.", "hyperf/framework": "Required to use BootProcessListener."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Process\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Process\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A process component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "process"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/redis", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/redis.git", "reference": "f0b858bec45b479d7b8cc4a70a792b144a6a680e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/redis/zipball/f0b858bec45b479d7b8cc4a70a792b144a6a680e", "reference": "f0b858bec45b479d7b8cc4a70a792b144a6a680e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-redis": "*", "hyperf/contract": "~2.2.0", "hyperf/pool": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0"}, "suggest": {"ext-redis": "Required to use sentinel mode (>=5.2).", "hyperf/di": "Create the RedisPool via dependency injection."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Redis\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Redis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A redis component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "pool", "redis"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/retry", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/retry.git", "reference": "48920f15d4cbf6adf7f1f79ba6c0954a8dc95649"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/retry/zipball/48920f15d4cbf6adf7f1f79ba6c0954a8dc95649", "reference": "48920f15d4cbf6adf7f1f79ba6c0954a8dc95649", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0"}, "suggest": {"hyperf/contract": "Required to use annotations.", "hyperf/di": "Required to use annotations."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Retry\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Retry\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A retry component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "retry"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/server", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/server.git", "reference": "1d3364520e37777a7ed83d8bc1597da42f8d6c1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/server/zipball/1d3364520e37777a7ed83d8bc1597da42f8d6c1b", "reference": "1d3364520e37777a7ed83d8bc1597da42f8d6c1b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0", "psr/event-dispatcher": "^1.0", "psr/log": "^1.0|^2.0|^3.0", "symfony/console": "^5.0"}, "suggest": {"hyperf/event": "Dump the info after server start.", "hyperf/framework": "Dump the info after server start."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Server\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A base server library for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "server", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/snowflake", "version": "v2.2.33.1", "source": {"type": "git", "url": "https://github.com/hyperf/snowflake.git", "reference": "7e982676083761f3eb8f9c5267cd2c7e1253917b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/snowflake/zipball/7e982676083761f3eb8f9c5267cd2c7e1253917b", "reference": "7e982676083761f3eb8f9c5267cd2c7e1253917b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "php": ">=7.2"}, "suggest": {"hyperf/config": "Required to read snowflake config.", "hyperf/redis": "Required to use RedisMilliSecondMetaGenerator or RedisSecondMetaGenerator.", "psr/container": "Required to use MetaGeneratorFactory."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Snowflake\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Snowflake\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A snowflake library", "homepage": "https://hyperf.io", "keywords": ["php", "snowflake"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-30T11:55:54+00:00"}, {"name": "hyperf/swagger", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/swagger.git", "reference": "6003836b68b676c90bdcdcb0e8cbcfa5fee6811a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/swagger/zipball/6003836b68b676c90bdcdcb0e8cbcfa5fee6811a", "reference": "6003836b68b676c90bdcdcb0e8cbcfa5fee6811a", "shasum": ""}, "require": {"hyperf/command": "~2.2.0", "php": ">=7.2", "zircote/swagger-php": "^3.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Swagger\\ConfigProvider"}, "branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Hyperf\\Swagger\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A swagger library for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swagger", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/translation", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/translation.git", "reference": "66c018e6e1605a446db212b0d3905e1d816dba77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/translation/zipball/66c018e6e1605a446db212b0d3905e1d816dba77", "reference": "66c018e6e1605a446db212b0d3905e1d816dba77", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/macroable": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Translation\\ConfigProvider"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Translation\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "An independent translation component, forked by illuminate/translation.", "keywords": ["hyperf", "translation"], "support": {"issues": "https://github.com/hyperf/translation/issues", "source": "https://github.com/hyperf/translation/tree/v2.2.33"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/utils", "version": "v2.2.34", "source": {"type": "git", "url": "https://github.com/hyperf/utils.git", "reference": "9c8519392166a6c8057cc52f7d02e1ac638581f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/utils/zipball/9c8519392166a6c8057cc52f7d02e1ac638581f5", "reference": "9c8519392166a6c8057cc52f7d02e1ac638581f5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/inflector": "^2.0", "hyperf/context": "~2.2.0", "hyperf/contract": "~2.2.0", "hyperf/engine": "^1.1", "hyperf/macroable": "~2.2.0", "php": ">=7.2"}, "suggest": {"ext-swoole": "Required to use methods related to swoole (>=4.5).", "hyperf/di": "Required to use ExceptionNormalizer", "nikic/php-parser": "Required to use PhpParser. (^4.0)", "symfony/property-access": "Required to use SymfonyNormalizer (^5.0)", "symfony/serializer": "Required to use SymfonyNormalizer (^5.0)", "symfony/var-dumper": "Required to use the dd function (^5.0)."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Utils\\ConfigProvider"}, "branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Utils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A tools package that could help developer solved the problem quickly.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "utils"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-07-21T05:42:54+00:00"}, {"name": "hyperf/validation", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/validation.git", "reference": "d4513923ed4cac88b4b5368bc6497b460465c1e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/validation/zipball/d4513923ed4cac88b4b5368bc6497b460465c1e0", "reference": "d4513923ed4cac88b4b5368bc6497b460465c1e0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"egulias/email-validator": "^3.0", "hyperf/contract": "~2.2.0", "hyperf/database": "~2.2.0", "hyperf/di": "~2.2.0", "hyperf/framework": "~2.2.0", "hyperf/http-server": "~2.2.0", "hyperf/macroable": "~2.2.0", "hyperf/translation": "~2.2.0", "hyperf/utils": "~2.2.0", "nesbot/carbon": "^2.21", "php": ">=7.2", "psr/container": "^1.0|^2.0", "psr/event-dispatcher": "^1.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Validation\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Validation\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "hyperf validation", "keywords": ["hyperf", "validation"], "support": {"issues": "https://github.com/hyperf/validation/issues", "source": "https://github.com/hyperf/validation/tree/v2.2.33"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/view", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/view.git", "reference": "09254f65cd4de25e2f8c27bd3336e12f8935fad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/view/zipball/09254f65cd4de25e2f8c27bd3336e12f8935fad3", "reference": "09254f65cd4de25e2f8c27bd3336e12f8935fad3", "shasum": ""}, "require": {"hyperf/contract": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "psr/container": "^1.0|^2.0"}, "suggest": {"duncan3dc/blade": "Required to use blade as a view render engine.", "hyperf/task": "Required to use task as a view render mode.", "league/plates": "Required to use plates as a view render engine.", "smarty/smarty": "Required to use smarty as a view render engine.", "twig/twig": "Required to use twig as a view render engine."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\View\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\View\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A view library for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "view"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "jaeger/g-http", "version": "V1.7.2", "source": {"type": "git", "url": "https://github.com/jae-jae/GHttp.git", "reference": "82585ddd5e2c6651e37ab1d8166efcdbb6b293d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jae-jae/GHttp/zipball/82585ddd5e2c6651e37ab1d8166efcdbb6b293d4", "reference": "82585ddd5e2c6651e37ab1d8166efcdbb6b293d4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"cache/filesystem-adapter": "^1", "guzzlehttp/guzzle": "^6.0 | ^7.0"}, "type": "library", "autoload": {"psr-4": {"Jaeger\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Simple Http client base on GuzzleHttp", "support": {"issues": "https://github.com/jae-jae/GHttp/issues", "source": "https://github.com/jae-jae/GHttp/tree/V1.7.2"}, "time": "2021-08-08T04:59:44+00:00"}, {"name": "jaeger/phpquery-single", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/jae-jae/phpQuery-single.git", "reference": "39a650ade692a6b480c22220dce0c198d6a946fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jae-jae/phpQuery-single/zipball/39a650ade692a6b480c22220dce0c198d6a946fb", "reference": "39a650ade692a6b480c22220dce0c198d6a946fb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"classmap": ["phpQuery.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/TobiaszCudnik", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "role": "Packager"}], "description": "phpQuery单文件版本，是Querylist的依赖(http://querylist.cc/)，phpQuery项目主页:http://code.google.com/p/phpquery/", "homepage": "http://code.google.com/p/phpquery/", "support": {"issues": "https://github.com/jae-jae/phpQuery-single/issues", "source": "https://github.com/jae-jae/phpQuery-single/tree/1.1.1"}, "time": "2022-03-26T15:01:16+00:00"}, {"name": "jaeger/querylist", "version": "V4.2.8", "source": {"type": "git", "url": "https://github.com/jae-jae/QueryList.git", "reference": "39dc0ca9c668bec7a793e20472ccd7d26ef89ea4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jae-jae/QueryList/zipball/39dc0ca9c668bec7a793e20472ccd7d26ef89ea4", "reference": "39dc0ca9c668bec7a793e20472ccd7d26ef89ea4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "jaeger/g-http": "^1.1", "jaeger/phpquery-single": "^1", "php": ">=7.1", "tightenco/collect": ">5.0"}, "require-dev": {"phpunit/phpunit": "^8.5", "symfony/var-dumper": "^3.3"}, "type": "library", "autoload": {"psr-4": {"QL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Simple, elegant, extensible PHP Web Scraper (crawler/spider),Use the css3 dom selector,Based on phpQuery! 简洁、优雅、可扩展的PHP采集工具(爬虫)，基于phpQuery。", "homepage": "http://querylist.cc", "keywords": ["QueryList", "php<PERSON><PERSON><PERSON>", "spider"], "support": {"issues": "https://github.com/jae-jae/QueryList/issues", "source": "https://github.com/jae-jae/QueryList/tree/V4.2.8"}, "funding": [{"url": "https://opencollective.com/querylist", "type": "open_collective"}], "time": "2021-07-05T06:07:58+00:00"}, {"name": "jaeger/querylist-puppeteer", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/jae-jae/QueryList-Puppeteer.git", "reference": "c6bd5acc4c95022bea92ba1308841b863eab2cf0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jae-jae/QueryList-Puppeteer/zipball/c6bd5acc4c95022bea92ba1308841b863eab2cf0", "reference": "c6bd5acc4c95022bea92ba1308841b863eab2cf0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"nesk/puphpeteer": "^1.4", "php": ">=7.1"}, "require-dev": {"jaeger/querylist": "dev-master"}, "type": "library", "autoload": {"psr-4": {"QL\\Ext\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "QueryList Plugin: Use Puppeteer to crawl Javascript dynamically rendered pages.(Headless Chrome ) 使用Puppeteer采集JavaScript动态渲染的页面", "support": {"issues": "https://github.com/jae-jae/QueryList-Puppeteer/issues", "source": "https://github.com/jae-jae/QueryList-Puppeteer/tree/4.0.0"}, "time": "2019-01-15T10:18:49+00:00"}, {"name": "jaybizzle/crawler-detect", "version": "v1.2.112", "source": {"type": "git", "url": "https://github.com/JayBizzle/Crawler-Detect.git", "reference": "2c555ce35a07a5c1c808cee7d5bb52c41a4c7b2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JayBizzle/Crawler-Detect/zipball/2c555ce35a07a5c1c808cee7d5bb52c41a4c7b2f", "reference": "2c555ce35a07a5c1c808cee7d5bb52c41a4c7b2f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "type": "library", "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "support": {"issues": "https://github.com/JayBizzle/Crawler-Detect/issues", "source": "https://github.com/JayBizzle/Crawler-Detect/tree/v1.2.112"}, "time": "2022-10-05T21:52:44+00:00"}, {"name": "jenssegers/agent", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/jenssegers/agent.git", "reference": "daa11c43729510b3700bc34d414664966b03bffe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jenssegers/agent/zipball/daa11c43729510b3700bc34d414664966b03bffe", "reference": "daa11c43729510b3700bc34d414664966b03bffe", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"jaybizzle/crawler-detect": "^1.2", "mobiledetect/mobiledetectlib": "^2.7.6", "php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5.0|^6.0|^7.0"}, "suggest": {"illuminate/support": "Required for laravel service providers"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}, "laravel": {"providers": ["Jenssegers\\Agent\\AgentServiceProvider"], "aliases": {"Agent": "Jenssegers\\Agent\\Facades\\Agent"}}}, "autoload": {"psr-4": {"Jenssegers\\Agent\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jenssegers.com"}], "description": "Desktop/mobile user agent parser with support for <PERSON><PERSON>, based on Mobiledetect", "homepage": "https://github.com/jenssegers/agent", "keywords": ["Agent", "browser", "desktop", "laravel", "mobile", "platform", "user agent", "useragent"], "support": {"issues": "https://github.com/jenssegers/agent/issues", "source": "https://github.com/jenssegers/agent/tree/v2.6.4"}, "funding": [{"url": "https://github.com/jenssegers", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/jenssegers/agent", "type": "tidelift"}], "time": "2020-06-13T08:05:20+00:00"}, {"name": "laminas/laminas-mime", "version": "2.10.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-mime.git", "reference": "62a899a7c9100889c2d2386b1357003a2cb52fa9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-mime/zipball/62a899a7c9100889c2d2386b1357003a2cb52fa9", "reference": "62a899a7c9100889c2d2386b1357003a2cb52fa9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"laminas/laminas-stdlib": "^2.7 || ^3.0", "php": "^7.3 || ~8.0.0 || ~8.1.0"}, "conflict": {"zendframework/zend-mime": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.2.1", "laminas/laminas-mail": "^2.12", "phpunit/phpunit": "^9.5"}, "suggest": {"laminas/laminas-mail": "Laminas\\Mail component"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Mime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Create and parse MIME messages and parts", "homepage": "https://laminas.dev", "keywords": ["laminas", "mime"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-mime/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-mime/issues", "rss": "https://github.com/laminas/laminas-mime/releases.atom", "source": "https://github.com/laminas/laminas-mime"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2022-08-30T09:38:41+00:00"}, {"name": "laminas/laminas-stdlib", "version": "3.13.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stdlib.git", "reference": "66a6d03c381f6c9f1dd988bf8244f9afb9380d76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stdlib/zipball/66a6d03c381f6c9f1dd988bf8244f9afb9380d76", "reference": "66a6d03c381f6c9f1dd988bf8244f9afb9380d76", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0"}, "conflict": {"zendframework/zend-stdlib": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.3.0", "phpbench/phpbench": "^1.2.6", "phpstan/phpdoc-parser": "^0.5.4", "phpunit/phpunit": "^9.5.23", "psalm/plugin-phpunit": "^0.17.0", "vimeo/psalm": "^4.26"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-stdlib/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-stdlib/issues", "rss": "https://github.com/laminas/laminas-stdlib/releases.atom", "source": "https://github.com/laminas/laminas-stdlib"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2022-08-24T13:56:50+00:00"}, {"name": "league/flysystem", "version": "1.1.10", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3239285c825c152bcc315fe0e87d6b55f5972ed1", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.10"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2022-10-04T09:16:37+00:00"}, {"name": "league/html-to-markdown", "version": "5.1.0", "source": {"type": "git", "url": "https://github.com/thephpleague/html-to-markdown.git", "reference": "e0fc8cf07bdabbcd3765341ecb50c34c271d64e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/html-to-markdown/zipball/e0fc8cf07bdabbcd3765341ecb50c34c271d64e1", "reference": "e0fc8cf07bdabbcd3765341ecb50c34c271d64e1", "shasum": ""}, "require": {"ext-dom": "*", "ext-xml": "*", "php": "^7.2.5 || ^8.0"}, "require-dev": {"mikehaertl/php-shellcommand": "^1.1.0", "phpstan/phpstan": "^0.12.99", "phpunit/phpunit": "^8.5 || ^9.2", "scrutinizer/ocular": "^1.6", "unleashedtech/php-coding-standard": "^2.7", "vimeo/psalm": "^4.22"}, "bin": ["bin/html-to-markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}, "autoload": {"psr-4": {"League\\HTMLToMarkdown\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://modernnerd.net", "role": "Original Author"}], "description": "An HTML-to-markdown conversion helper for PHP", "homepage": "https://github.com/thephpleague/html-to-markdown", "keywords": ["html", "markdown"], "support": {"issues": "https://github.com/thephpleague/html-to-markdown/issues", "source": "https://github.com/thephpleague/html-to-markdown/tree/5.1.0"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/html-to-markdown", "type": "tidelift"}], "time": "2022-03-02T17:24:08+00:00"}, {"name": "league/mime-type-detection", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "ff6248ea87a9f116e78edd6002e39e5128a0d4dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ff6248ea87a9f116e78edd6002e39e5128a0d4dd", "reference": "ff6248ea87a9f116e78edd6002e39e5128a0d4dd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.11.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2022-04-17T13:12:02+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.2.5", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c31a03f3b707876641875428cda9ecb4d05c3823"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c31a03f3b707876641875428cda9ecb4d05c3823", "reference": "c31a03f3b707876641875428cda9ecb4d05c3823", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"myclabs/php-enum": "^1.5", "php": "^7.4 || ^8.0", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.9", "guzzlehttp/guzzle": "^6.5.3 || ^7.2.0", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.4", "phpunit/phpunit": "^8.5.8 || ^9.4.2", "vimeo/psalm": "^4.1"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/2.2.5"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}, {"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "time": "2022-11-25T18:21:57+00:00"}, {"name": "markbaker/complex", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "ab8bc271e404909db09ff2d5ffa1e538085c0f22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/ab8bc271e404909db09ff2d5ffa1e538085c0f22", "reference": "ab8bc271e404909db09ff2d5ffa1e538085c0f22", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "squizlabs/php_codesniffer": "^3.4"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.1"}, "time": "2021-06-29T15:32:53+00:00"}, {"name": "markbaker/matrix", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "c66aefcafb4f6c269510e9ac46b82619a904c576"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/c66aefcafb4f6c269510e9ac46b82619a904c576", "reference": "c66aefcafb4f6c269510e9ac46b82619a904c576", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.0"}, "time": "2021-07-01T19:01:15+00:00"}, {"name": "michelf/php-markdown", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/michelf/php-markdown.git", "reference": "5024d623c1a057dcd2d076d25b7d270a1d0d55f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/michelf/php-markdown/zipball/5024d623c1a057dcd2d076d25b7d270a1d0d55f3", "reference": "5024d623c1a057dcd2d076d25b7d270a1d0d55f3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.3 <5.8"}, "type": "library", "autoload": {"psr-4": {"Michelf\\": "<PERSON><PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://michelf.ca/", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://daringfireball.net/"}], "description": "PHP <PERSON>", "homepage": "https://michelf.ca/projects/php-markdown/", "keywords": ["markdown"], "support": {"issues": "https://github.com/michelf/php-markdown/issues", "source": "https://github.com/michelf/php-markdown/tree/1.9.1"}, "time": "2021-11-24T02:52:38+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.41", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "fc9cccd4d3706d5a7537b562b59cc18f9e4c0cb1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/fc9cccd4d3706d5a7537b562b59cc18f9e4c0cb1", "reference": "fc9cccd4d3706d5a7537b562b59cc18f9e4c0cb1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.35||~5.7"}, "type": "library", "autoload": {"psr-0": {"Detection": "namespaced/"}, "classmap": ["Mobile_Detect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/2.8.41"}, "time": "2022-11-08T18:31:26+00:00"}, {"name": "monolog/monolog", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "4192345e260f1d51b365536199744b987e160edc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/4192345e260f1d51b365536199744b987e160edc", "reference": "4192345e260f1d51b365536199744b987e160edc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "graylog2/gelf-php": "^1.4.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": ">=0.90@dev", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.5.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-04-08T15:43:54+00:00"}, {"name": "mpdf/mpdf", "version": "v8.2.5", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "e175b05e3e00977b85feb96a8cccb174ac63621f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/e175b05e3e00977b85feb96a8cccb174ac63621f", "reference": "e175b05e3e00977b85feb96a8cccb174ac63621f", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "mpdf/psr-http-message-shim": "^1.0 || ^2.0", "mpdf/psr-log-aware-trait": "^2.0 || ^3.0", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-message": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "~2.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "https://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "time": "2024-11-18T15:30:42+00:00"}, {"name": "mpdf/psr-http-message-shim", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/mpdf/psr-http-message-shim.git", "reference": "3206e6b80b6d2479e148ee497e9f2bebadc919db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-http-message-shim/zipball/3206e6b80b6d2479e148ee497e9f2bebadc919db", "reference": "3206e6b80b6d2479e148ee497e9f2bebadc919db", "shasum": ""}, "require": {"psr/http-message": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrHttpMessageShim\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Shim to allow support of different psr/message versions.", "support": {"issues": "https://github.com/mpdf/psr-http-message-shim/issues", "source": "https://github.com/mpdf/psr-http-message-shim/tree/1.0.0"}, "time": "2023-09-01T05:59:47+00:00"}, {"name": "mpdf/psr-log-aware-trait", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/mpdf/psr-log-aware-trait.git", "reference": "7a077416e8f39eb626dee4246e0af99dd9ace275"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-log-aware-trait/zipball/7a077416e8f39eb626dee4246e0af99dd9ace275", "reference": "7a077416e8f39eb626dee4246e0af99dd9ace275", "shasum": ""}, "require": {"psr/log": "^1.0 || ^2.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrLogAwareTrait\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Trait to allow support of different psr/log versions.", "support": {"issues": "https://github.com/mpdf/psr-log-aware-trait/issues", "source": "https://github.com/mpdf/psr-log-aware-trait/tree/v2.0.0"}, "time": "2023-05-03T06:18:28+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/14daed4296fae74d9e3201d2c4925d1acb7aa614", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2022-03-03T13:19:32+00:00"}, {"name": "myclabs/php-enum", "version": "1.8.3", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "b942d263c641ddb5190929ff840c68f78713e937"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/b942d263c641ddb5190929ff840c68f78713e937", "reference": "b942d263c641ddb5190929ff840c68f78713e937", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.6.2"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.3"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2021-07-05T08:18:36+00:00"}, {"name": "nesbot/carbon", "version": "2.62.1", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "01bc4cdefe98ef58d1f9cb31bdbbddddf2a88f7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/01bc4cdefe98ef58d1f9cb31bdbbddddf2a88f7a", "reference": "01bc4cdefe98ef58d1f9cb31bdbbddddf2a88f7a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.0", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2022-09-02T07:48:13+00:00"}, {"name": "nesk/puphpeteer", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/nesk/puphpeteer.git", "reference": "21adf25d320f32b005cb3e2f9026616566015fcc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nesk/puphpeteer/zipball/21adf25d320f32b005cb3e2f9026616566015fcc", "reference": "21adf25d320f32b005cb3e2f9026616566015fcc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"nesk/rialto": "^1.2.0", "php": ">=7.1", "psr/log": "^1.0", "vierbergenlars/php-semver": "^3.0.2"}, "require-dev": {"codedungeon/phpunit-result-printer": ">=0.6 <1.0", "monolog/monolog": "^1.23", "phpunit/phpunit": "^6.5|^7.0", "symfony/process": "^4.0"}, "type": "library", "autoload": {"psr-4": {"Nesk\\Puphpeteer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A Puppeteer bridge for PHP, supporting the entire API.", "keywords": ["automation", "developer-tools", "headless-chrome", "php", "puppeteer", "testing", "web"], "support": {"issues": "https://github.com/nesk/puphpeteer/issues", "source": "https://github.com/nesk/puphpeteer/tree/1.6.0"}, "abandoned": true, "time": "2019-06-24T11:12:21+00:00"}, {"name": "nesk/rialto", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/nesk/rialto.git", "reference": "a3db615d845cca42135fa1e271b323ffe904b83c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nesk/rialto/zipball/a3db615d845cca42135fa1e271b323ffe904b83c", "reference": "a3db615d845cca42135fa1e271b323ffe904b83c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"clue/socket-raw": "^1.2", "php": ">=7.1", "psr/log": "^1.0", "symfony/process": "^3.3|^4.0|^5.0"}, "require-dev": {"codedungeon/phpunit-result-printer": ">=0.6 <1.0", "monolog/monolog": "^1.23", "phpunit/phpunit": "^6.5|^7.0"}, "suggest": {"ext-weakref": "Required to run all the tests"}, "type": "library", "autoload": {"psr-4": {"Nesk\\Rialto\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Manage Node resources from PHP", "keywords": ["Bridge", "Socket", "communication", "node", "php", "wrapper"], "support": {"issues": "https://github.com/nesk/rialto/issues", "source": "https://github.com/nesk/rialto/tree/1.4.0"}, "abandoned": true, "time": "2020-04-12T13:11:08+00:00"}, {"name": "nette/php-generator", "version": "v3.6.9", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "d31782f7bd2ae84ad06f863391ec3fb77ca4d0a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/d31782f7bd2ae84ad06f863391ec3fb77ca4d0a6", "reference": "d31782f7bd2ae84ad06f863391ec3fb77ca4d0a6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"nette/utils": "^3.1.2", "php": ">=7.2 <8.3"}, "require-dev": {"nette/tester": "^2.4", "nikic/php-parser": "^4.13", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.8"}, "suggest": {"nikic/php-parser": "to use ClassType::withBodiesFrom() & GlobalFunction::withBodyFrom()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.1 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/v3.6.9"}, "time": "2022-10-04T11:49:47+00:00"}, {"name": "nette/utils", "version": "v3.2.8", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "a6784d523c0e67409b5c64c3d951e9871ef64241"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/a6784d523c0e67409b5c64c3d951e9871ef64241", "reference": "a6784d523c0e67409b5c64c3d951e9871ef64241", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2 <8.3"}, "conflict": {"nette/di": "<3.0.6"}, "require-dev": {"nette/tester": "~2.0", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.8"}, "time": "2022-09-06T00:55:00+00:00"}, {"name": "nikic/fast-route", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/nikic/FastRoute.git", "reference": "181d480e08d9476e61381e04a71b34dc0432e812"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/FastRoute/zipball/181d480e08d9476e61381e04a71b34dc0432e812", "reference": "181d480e08d9476e61381e04a71b34dc0432e812", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|~5.7"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"FastRoute\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fast request router for PHP", "keywords": ["router", "routing"], "support": {"issues": "https://github.com/nikic/FastRoute/issues", "source": "https://github.com/nikic/FastRoute/tree/master"}, "time": "2018-02-13T20:26:39+00:00"}, {"name": "nikic/php-parser", "version": "v4.15.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "0ef6c55a3f47f89d7a374e6f835197a0b5fcf900"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/0ef6c55a3f47f89d7a374e6f835197a0b5fcf900", "reference": "0ef6c55a3f47f89d7a374e6f835197a0b5fcf900", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.15.1"}, "time": "2022-09-04T07:30:47+00:00"}, {"name": "overtrue/pinyin", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/overtrue/pinyin.git", "reference": "04bdb4d33d50e8fb1aa5a824064c5151c4b15dc2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/pinyin/zipball/04bdb4d33d50e8fb1aa5a824064c5151c4b15dc2", "reference": "04bdb4d33d50e8fb1aa5a824064c5151c4b15dc2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "require-dev": {"brainmaestro/composer-git-hooks": "^2.7", "friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": "~8.0"}, "type": "library", "extra": {"hooks": {"pre-commit": ["composer test", "composer fix-style"], "pre-push": ["composer test", "composer check-style"]}}, "autoload": {"files": ["src/const.php"], "psr-4": {"Overtrue\\Pinyin\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>", "homepage": "http://github.com/overtrue"}], "description": "Chinese to pinyin translator.", "homepage": "https://github.com/overtrue/pinyin", "keywords": ["Chinese", "<PERSON><PERSON><PERSON>", "cn2pinyin"], "support": {"issues": "https://github.com/overtrue/pinyin/issues", "source": "https://github.com/overtrue/pinyin/tree/4.0.8"}, "funding": [{"url": "https://www.patreon.com/overtrue", "type": "patreon"}], "time": "2021-07-19T03:43:32+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "php-di/phpdoc-reader", "version": "2.2.1", "source": {"type": "git", "url": "https://github.com/PHP-DI/PhpDocReader.git", "reference": "66daff34cbd2627740ffec9469ffbac9f8c8185c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/66daff34cbd2627740ffec9469ffbac9f8c8185c", "reference": "66daff34cbd2627740ffec9469ffbac9f8c8185c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.0"}, "require-dev": {"mnapoli/hard-mode": "~0.3.0", "phpunit/phpunit": "^8.5|^9.0"}, "type": "library", "autoload": {"psr-4": {"PhpDocReader\\": "src/PhpDocReader"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PhpDocReader parses @var and @param values in PHP docblocks (supports namespaced class names with the same resolution rules as PHP)", "keywords": ["phpdoc", "reflection"], "support": {"issues": "https://github.com/PHP-DI/PhpDocReader/issues", "source": "https://github.com/PHP-DI/PhpDocReader/tree/2.2.1"}, "time": "2020-10-12T12:39:22+00:00"}, {"name": "phpmailer/phpmailer", "version": "v6.8.1", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "e88da8d679acc3824ff231fdc553565b802ac016"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/e88da8d679acc3824ff231fdc553565b802ac016", "reference": "e88da8d679acc3824ff231fdc553565b802ac016", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "php": ">=5.5.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "doctrine/annotations": "^1.2.6 || ^1.13.3", "php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.7.2", "yoast/phpunit-polyfills": "^1.0.4"}, "suggest": {"ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "ext-openssl": "Needed for secure SMTP sending and DKIM signing", "greew/oauth2-azure-provider": "Needed for Microsoft Azure XOAUTH2 authentication", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)", "thenetworg/oauth2-azure": "Needed for Microsoft XOAUTH2 authentication"}, "type": "library", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "support": {"issues": "https://github.com/PHPMailer/PHPMailer/issues", "source": "https://github.com/PHPMailer/PHPMailer/tree/v6.8.1"}, "funding": [{"url": "https://github.com/Synchro", "type": "github"}], "time": "2023-08-29T08:26:30+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.29.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "fde2ccf55eaef7e86021ff1acce26479160a0fa0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/fde2ccf55eaef7e86021ff1acce26479160a0fa0", "reference": "fde2ccf55eaef7e86021ff1acce26479160a0fa0", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.15", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.4 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^1.0 || ^2.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5 || ^9.0 || ^10.0", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.29.0"}, "time": "2023-06-14T22:48:31+00:00"}, {"name": "phpoption/phpoption", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15", "reference": "eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^6.5.14 || ^7.5.20 || ^8.5.19 || ^9.5.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.8.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2021-12-04T23:24:31+00:00"}, {"name": "poliander/cron", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/poliander/cron.git", "reference": "ca727f1a47c8d48dafc73b81b8de7daa3c027d69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/poliander/cron/zipball/ca727f1a47c8d48dafc73b81b8de7daa3c027d69", "reference": "ca727f1a47c8d48dafc73b81b8de7daa3c027d69", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "7.4.* || 8.0.* || 8.1.* || 8.2.*"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "type": "library", "autoload": {"psr-4": {"Poliander\\Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "role": "Developer"}], "description": "Standard (V7) compliant crontab expression parser/validator with support for time zones", "homepage": "https://github.com/poliander/cron", "support": {"issues": "https://github.com/poliander/cron/issues", "source": "https://github.com/poliander/cron/tree/3.0.4"}, "time": "2022-12-29T20:01:35+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-06-29T06:28:15+00:00"}, {"name": "psr/http-factory", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "time": "2019-04-30T12:38:16+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/http-server-handler", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "aff2f80e33b7f026ec96bb42f63242dc50ffcae7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-handler/zipball/aff2f80e33b7f026ec96bb42f63242dc50ffcae7", "reference": "aff2f80e33b7f026ec96bb42f63242dc50ffcae7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"issues": "https://github.com/php-fig/http-server-handler/issues", "source": "https://github.com/php-fig/http-server-handler/tree/master"}, "time": "2018-10-30T16:46:14+00:00"}, {"name": "psr/http-server-middleware", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-middleware.git", "reference": "2296f45510945530b9dceb8bcedb5cb84d40c5f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-middleware/zipball/2296f45510945530b9dceb8bcedb5cb84d40c5f5", "reference": "2296f45510945530b9dceb8bcedb5cb84d40c5f5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0", "psr/http-message": "^1.0", "psr/http-server-handler": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-server-middleware/issues", "source": "https://github.com/php-fig/http-server-middleware/tree/master"}, "time": "2018-10-30T17:12:04+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "react/promise", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "234f8fd1023c9158e2314fa9d7d0e6a83db42910"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/234f8fd1023c9158e2314fa9d7d0e6a83db42910", "reference": "234f8fd1023c9158e2314fa9d7d0e6a83db42910", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.9.0"}, "funding": [{"url": "https://github.com/WyriHaximus", "type": "github"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-02-11T10:27:51+00:00"}, {"name": "setasign/fpdi", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "67c31f5e50c93c20579ca9e23035d8c540b51941"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/67c31f5e50c93c20579ca9e23035d8c540b51941", "reference": "67c31f5e50c93c20579ca9e23035d8c540b51941", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^7.1 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "^7", "setasign/fpdf": "~1.8.6", "setasign/tfpdf": "~1.33", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.6.3"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "time": "2025-02-05T13:22:35+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2021-10-18T15:26:12+00:00"}, {"name": "symfony/console", "version": "v5.4.11", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "535846c7ee6bc4dd027ca0d93220601456734b10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/535846c7ee6bc4dd027ca0d93220601456734b10", "reference": "535846c7ee6bc4dd027ca0d93220601456734b10", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v5.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-22T10:42:43+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/finder", "version": "v5.4.11", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "7872a66f57caffa2916a584db1aa7f12adc76f8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/7872a66f57caffa2916a584db1aa7f12adc76f8c", "reference": "7872a66f57caffa2916a584db1aa7f12adc76f8c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v5.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-29T07:37:50+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "5bbc823adecdae860bb64756d639ecfec17b050a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/5bbc823adecdae860bb64756d639ecfec17b050a", "reference": "5bbc823adecdae860bb64756d639ecfec17b050a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "927013f3aac555983a5059aada98e1907d842695"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/927013f3aac555983a5059aada98e1907d842695", "reference": "927013f3aac555983a5059aada98e1907d842695", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "511a08c03c1960e08a883f4cffcacd219b758354"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/511a08c03c1960e08a883f4cffcacd219b758354", "reference": "511a08c03c1960e08a883f4cffcacd219b758354", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "639084e360537a19f9ee352433b84ce831f3d2da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/639084e360537a19f9ee352433b84ce831f3d2da", "reference": "639084e360537a19f9ee352433b84ce831f3d2da", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "19bd1e4fcd5b91116f14d8533c57831ed00571b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/19bd1e4fcd5b91116f14d8533c57831ed00571b6", "reference": "19bd1e4fcd5b91116f14d8533c57831ed00571b6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "8ad114f6b39e2c98a8b0e3bd907732c207c2b534"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/8ad114f6b39e2c98a8b0e3bd907732c207c2b534", "reference": "8ad114f6b39e2c98a8b0e3bd907732c207c2b534", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "869329b1e9894268a8a61dabb69153029b7a8c97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/869329b1e9894268a8a61dabb69153029b7a8c97", "reference": "869329b1e9894268a8a61dabb69153029b7a8c97", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "9e8ecb5f92152187c4799efd3c96b78ccab18ff9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/9e8ecb5f92152187c4799efd3c96b78ccab18ff9", "reference": "9e8ecb5f92152187c4799efd3c96b78ccab18ff9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936", "reference": "7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/process", "version": "v5.4.11", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "6e75fe6874cbc7e4773d049616ab450eff537bf1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/6e75fe6874cbc7e4773d049616ab450eff537bf1", "reference": "6e75fe6874cbc7e4773d049616ab450eff537bf1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v5.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T16:58:25+00:00"}, {"name": "symfony/service-contracts", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "191afdcb5804db960d26d8566b7e9a2843cab3a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/191afdcb5804db960d26d8566b7e9a2843cab3a0", "reference": "191afdcb5804db960d26d8566b7e9a2843cab3a0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1.3"}, "suggest": {"psr/container": "", "symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v1.1.2"}, "time": "2019-05-28T07:50:59+00:00"}, {"name": "symfony/string", "version": "v5.4.15", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "571334ce9f687e3e6af72db4d3b2a9431e4fd9ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/571334ce9f687e3e6af72db4d3b2a9431e4fd9ed", "reference": "571334ce9f687e3e6af72db4d3b2a9431e4fd9ed", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "conflict": {"symfony/translation-contracts": ">=3.0"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v5.4.15"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-10-05T15:16:54+00:00"}, {"name": "symfony/translation", "version": "v5.4.14", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "f0ed07675863aa6e3939df8b1bc879450b585cab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/f0ed07675863aa6e3939df8b1bc879450b585cab", "reference": "f0ed07675863aa6e3939df8b1bc879450b585cab", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^2.3"}, "conflict": {"symfony/config": "<4.4", "symfony/console": "<5.3", "symfony/dependency-injection": "<5.0", "symfony/http-kernel": "<5.0", "symfony/twig-bundle": "<5.0", "symfony/yaml": "<4.4"}, "provide": {"symfony/translation-implementation": "2.3"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v5.4.14"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-10-07T08:01:20+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "136b19dd05cdf0709db6537d058bcab6dd6e2dbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/136b19dd05cdf0709db6537d058bcab6dd6e2dbe", "reference": "136b19dd05cdf0709db6537d058bcab6dd6e2dbe", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T16:58:25+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.14", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "6894d06145fefebd9a4c7272baa026a1c394a430"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/6894d06145fefebd9a4c7272baa026a1c394a430", "reference": "6894d06145fefebd9a4c7272baa026a1c394a430", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpunit/phpunit": "<5.4.3", "symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/uid": "^5.1|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v5.4.14"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-10-07T08:01:20+00:00"}, {"name": "symfony/yaml", "version": "v5.4.23", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "4cd2e3ea301aadd76a4172756296fe552fb45b0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/4cd2e3ea301aadd76a4172756296fe552fb45b0b", "reference": "4cd2e3ea301aadd76a4172756296fe552fb45b0b", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.3"}, "require-dev": {"symfony/console": "^5.3|^6.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-04-23T19:33:36+00:00"}, {"name": "tightenco/collect", "version": "v8.83.25", "source": {"type": "git", "url": "https://github.com/tighten/collect.git", "reference": "7d2a6fc5e97c5f7209a780bea98f35042c1fd0ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tighten/collect/zipball/7d2a6fc5e97c5f7209a780bea98f35042c1fd0ea", "reference": "7d2a6fc5e97c5f7209a780bea98f35042c1fd0ea", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.3|^8.0", "symfony/var-dumper": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"mockery/mockery": "^1.0", "nesbot/carbon": "^2.23.0", "phpunit/phpunit": "^8.3"}, "type": "library", "autoload": {"files": ["src/Collect/Support/helpers.php", "src/Collect/Support/alias.php"], "psr-4": {"Tightenco\\Collect\\": "src/Collect"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Collect - Illuminate Collections as a separate package.", "keywords": ["collection", "laravel"], "support": {"issues": "https://github.com/tighten/collect/issues", "source": "https://github.com/tighten/collect/tree/v8.83.25"}, "time": "2022-08-22T17:55:07+00:00"}, {"name": "vierbergenlars/php-semver", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/vierbergenlars/php-semver.git", "reference": "0db3f3147162453642bc90f611d5f5adfcedc306"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vierbergenlars/php-semver/zipball/0db3f3147162453642bc90f611d5f5adfcedc306", "reference": "0db3f3147162453642bc90f611d5f5adfcedc306", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^9.5.21"}, "bin": ["bin/semver", "bin/update-versions"], "type": "library", "autoload": {"psr-0": {"vierbergenlars\\LibJs\\": "src/", "vierbergenlars\\SemVer\\": "src/"}, "classmap": ["src/vierbergenlars/SemVer/internal.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Semantic Versioner for PHP", "keywords": ["semantic", "semver", "versioning"], "support": {"issues": "https://github.com/vierbergenlars/php-semver/issues", "source": "https://github.com/vierbergenlars/php-semver/tree/v3.0.3"}, "time": "2022-08-22T06:58:37+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.4.1", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "264dce589e7ce37a7ba99cb901eed8249fbec92f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/264dce589e7ce37a7ba99cb901eed8249fbec92f", "reference": "264dce589e7ce37a7ba99cb901eed8249fbec92f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.0.2", "php": "^7.1.3 || ^8.0", "phpoption/phpoption": "^1.8", "symfony/polyfill-ctype": "^1.23", "symfony/polyfill-mbstring": "^1.23.1", "symfony/polyfill-php80": "^1.23.1"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-filter": "*", "phpunit/phpunit": "^7.5.20 || ^8.5.21 || ^9.5.10"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.4.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2021-12-12T23:22:04+00:00"}, {"name": "yurunsoft/phpmailer-swoole", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/Yurunsoft/PHPMailer-Swoole.git", "reference": "ec09ff66f6e9eb8b94b8574545357dc52e91c0a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Yurunsoft/PHPMailer-Swoole/zipball/ec09ff66f6e9eb8b94b8574545357dc52e91c0a5", "reference": "ec09ff66f6e9eb8b94b8574545357dc52e91c0a5", "shasum": ""}, "require": {"php": ">=7.0", "phpmailer/phpmailer": "~6.0"}, "require-dev": {"swoft/swoole-ide-helper": "~2.0"}, "type": "library", "autoload": {"files": ["src/PHPMailer/functions.php"], "psr-4": {"PHPMailer\\PHPMailer\\": "src/PHPMailer", "Yurun\\Util\\Swoole\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "description": "PHPMailer 支持 Swoole 协程环境", "support": {"issues": "https://github.com/Yurunsoft/PHPMailer-Swoole/issues", "source": "https://github.com/Yurunsoft/PHPMailer-Swoole/tree/master"}, "time": "2019-10-24T07:46:26+00:00"}, {"name": "zircote/swagger-php", "version": "3.3.7", "source": {"type": "git", "url": "https://github.com/zircote/swagger-php.git", "reference": "e8c3bb316e385e93a0c7e8b2aa0681079244c381"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zircote/swagger-php/zipball/e8c3bb316e385e93a0c7e8b2aa0681079244c381", "reference": "e8c3bb316e385e93a0c7e8b2aa0681079244c381", "shasum": ""}, "require": {"doctrine/annotations": "^1.7", "ext-json": "*", "php": ">=7.2", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"composer/package-versions-deprecated": "*********", "friendsofphp/php-cs-fixer": "^2.17 || ^3.0", "phpunit/phpunit": ">=8.5.14"}, "bin": ["bin/openapi"], "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://bfanger.nl"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://radebatz.net"}], "description": "swagger-php - Generate interactive documentation for your RESTful API using phpdoc annotations", "homepage": "https://github.com/zircote/swagger-php/", "keywords": ["api", "json", "rest", "service discovery"], "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.3.7"}, "time": "2023-01-03T21:17:10+00:00"}], "packages-dev": [{"name": "composer/pcre", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "4482b6409ca6bfc2af043a5711cd21ac3e7a8dfb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/4482b6409ca6bfc2af043a5711cd21ac3e7a8dfb", "reference": "4482b6409ca6bfc2af043a5711cd21ac3e7a8dfb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.0.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-11-03T20:24:16+00:00"}, {"name": "composer/semver", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "3953f23262f2bff1919fc82183ad9acb13ff62c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/3953f23262f2bff1919fc82183ad9acb13ff62c9", "reference": "3953f23262f2bff1919fc82183ad9acb13ff62c9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-04-01T19:23:25+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "ced299686f41dce890debac69273b47ffe98a40c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/ced299686f41dce890debac69273b47ffe98a40c", "reference": "ced299686f41dce890debac69273b47ffe98a40c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^6.0"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-25T21:32:43+00:00"}, {"name": "friendsofphp/php-cs-fixer", "version": "v3.12.0", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/PHP-CS-Fixer.git", "reference": "eae11d945e2885d86e1c080eec1bb30a2aa27998"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/PHP-CS-Fixer/zipball/eae11d945e2885d86e1c080eec1bb30a2aa27998", "reference": "eae11d945e2885d86e1c080eec1bb30a2aa27998", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"composer/semver": "^3.2", "composer/xdebug-handler": "^3.0.3", "doctrine/annotations": "^1.13", "ext-json": "*", "ext-tokenizer": "*", "php": "^7.4 || ^8.0", "sebastian/diff": "^4.0", "symfony/console": "^5.4 || ^6.0", "symfony/event-dispatcher": "^5.4 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0", "symfony/finder": "^5.4 || ^6.0", "symfony/options-resolver": "^5.4 || ^6.0", "symfony/polyfill-mbstring": "^1.23", "symfony/polyfill-php80": "^1.25", "symfony/polyfill-php81": "^1.25", "symfony/process": "^5.4 || ^6.0", "symfony/stopwatch": "^5.4 || ^6.0"}, "require-dev": {"justinrainbow/json-schema": "^5.2", "keradus/cli-executor": "^1.5", "mikey179/vfsstream": "^1.6.10", "php-coveralls/php-coveralls": "^2.5.2", "php-cs-fixer/accessible-object": "^1.1", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.2", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.2.1", "phpspec/prophecy": "^1.15", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "phpunitgoodpractices/polyfill": "^1.6", "phpunitgoodpractices/traits": "^1.9.2", "symfony/phpunit-bridge": "^6.0", "symfony/yaml": "^5.4 || ^6.0"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters."}, "bin": ["php-cs-fixer"], "type": "application", "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "support": {"issues": "https://github.com/FriendsOfPHP/PHP-CS-Fixer/issues", "source": "https://github.com/FriendsOfPHP/PHP-CS-Fixer/tree/v3.12.0"}, "funding": [{"url": "https://github.com/keradus", "type": "github"}], "time": "2022-10-12T14:20:51+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "hyperf/ide-helper", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/ide-helper.git", "reference": "e7e26af552f7cf5512c357df9604ff9aa77ed3f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/ide-helper/zipball/e7e26af552f7cf5512c357df9604ff9aa77ed3f3", "reference": "e7e26af552f7cf5512c357df9604ff9aa77ed3f3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "IDE help files for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "ide-helper", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/testing", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/testing.git", "reference": "63726e3b4e999a96dd4dd62a54f8b2f8f48f850e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/testing/zipball/63726e3b4e999a96dd4dd62a54f8b2f8f48f850e", "reference": "63726e3b4e999a96dd4dd62a54f8b2f8f48f850e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hyperf/contract": "~2.2.0", "hyperf/http-message": "~2.2.0", "hyperf/http-server": "~2.2.0", "hyperf/utils": "~2.2.0", "php": ">=7.2", "phpunit/phpunit": "^9.5", "psr/container": "^1.0|^2.0"}, "bin": ["co-php<PERSON>t"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Hyperf\\Testing\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Testing for hyperf", "keywords": ["php", "swoole", "testing"], "support": {"source": "https://github.com/hyperf/testing/tree/v2.2.33"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "hyperf/watcher", "version": "v2.2.33", "source": {"type": "git", "url": "https://github.com/hyperf/watcher.git", "reference": "bee6a3dbc67a355fc608d44242f1be8cfec97b2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/watcher/zipball/bee6a3dbc67a355fc608d44242f1be8cfec97b2f", "reference": "bee6a3dbc67a355fc608d44242f1be8cfec97b2f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-swoole": ">=4.5", "hyperf/command": "~2.2.0", "hyperf/di": "~2.2.0", "hyperf/framework": "~2.2.0", "php": ">=7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "hyperf": {"config": "Hyperf\\Watcher\\ConfigProvider"}}, "autoload": {"psr-4": {"Hyperf\\Watcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Hot reload watcher for Hyperf", "keywords": ["hyperf", "php"], "support": {"issues": "https://github.com/hyperf/watcher/issues", "source": "https://github.com/hyperf/watcher/tree/v2.2.33"}, "time": "2022-05-24T13:10:54+00:00"}, {"name": "mockery/mockery", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "e92dcc83d5a51851baf5f5591d32cb2b16e3684e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/e92dcc83d5a51851baf5f5591d32cb2b16e3684e", "reference": "e92dcc83d5a51851baf5f5591d32cb2b16e3684e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": "^7.3 || ^8.0"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.5.1"}, "time": "2022-09-07T15:32:08+00:00"}, {"name": "phar-io/manifest", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "97803eca37d319dfa7826cc2437fc020857acb53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/97803eca37d319dfa7826cc2437fc020857acb53", "reference": "97803eca37d319dfa7826cc2437fc020857acb53", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.3"}, "time": "2021-07-20T11:28:43+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpstan/phpstan", "version": "0.12.100", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "48236ddf823547081b2b153d1cd2994b784328c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/48236ddf823547081b2b153d1cd2994b784328c3", "reference": "48236ddf823547081b2b153d1cd2994b784328c3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "extra": {"branch-alias": {"dev-master": "0.12-dev"}}, "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "support": {"issues": "https://github.com/phpstan/phpstan/issues", "source": "https://github.com/phpstan/phpstan/tree/0.12.100"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpstan/phpstan", "type": "tidelift"}], "time": "2022-11-01T09:52:08+00:00"}, {"name": "phpunit/php-code-coverage", "version": "9.2.19", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "c77b56b63e3d2031bd8997fcec43c1925ae46559"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/c77b56b63e3d2031bd8997fcec43c1925ae46559", "reference": "c77b56b63e3d2031bd8997fcec43c1925ae46559", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.14", "php": ">=7.3", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0.3", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcov": "*", "ext-xdebug": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.19"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-11-18T07:47:47+00:00"}, {"name": "phpunit/php-file-iterator", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-12-02T12:48:52+00:00"}, {"name": "phpunit/php-invoker", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:58:55+00:00"}, {"name": "phpunit/php-text-template", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T05:33:50+00:00"}, {"name": "phpunit/php-timer", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:16:10+00:00"}, {"name": "phpunit/phpunit", "version": "9.5.25", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "3e6f90ca7e3d02025b1d147bd8d4a89fd4ca8a1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/phpunit/zipball/3e6f90ca7e3d02025b1d147bd8d4a89fd4ca8a1d", "reference": "3e6f90ca7e3d02025b1d147bd8d4a89fd4ca8a1d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/instantiator": "^1.3.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.1", "phar-io/manifest": "^2.0.3", "phar-io/version": "^3.0.2", "php": ">=7.3", "phpunit/php-code-coverage": "^9.2.13", "phpunit/php-file-iterator": "^3.0.5", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.3", "phpunit/php-timer": "^5.0.2", "sebastian/cli-parser": "^1.0.1", "sebastian/code-unit": "^1.0.6", "sebastian/comparator": "^4.0.8", "sebastian/diff": "^4.0.3", "sebastian/environment": "^5.1.3", "sebastian/exporter": "^4.0.5", "sebastian/global-state": "^5.0.1", "sebastian/object-enumerator": "^4.0.3", "sebastian/resource-operations": "^3.0.3", "sebastian/type": "^3.2", "sebastian/version": "^3.0.2"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "9.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/9.5.25"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2022-09-25T03:44:45+00:00"}, {"name": "sebastian/cli-parser", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "442e7c7e687e42adc03470c7b668bc4b2402c0b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/442e7c7e687e42adc03470c7b668bc4b2402c0b2", "reference": "442e7c7e687e42adc03470c7b668bc4b2402c0b2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:08:49+00:00"}, {"name": "sebastian/code-unit", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/1fc9f64c0927627ef78ba436c9b17d967e68e120", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:08:54+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:30:19+00:00"}, {"name": "sebastian/comparator", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "fa0f136dd2334583309d32b62544682ee972b51a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/fa0f136dd2334583309d32b62544682ee972b51a", "reference": "fa0f136dd2334583309d32b62544682ee972b51a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T12:41:17+00:00"}, {"name": "sebastian/complexity", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "739b35e53379900cc9ac327b2147867b8b6efd88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/739b35e53379900cc9ac327b2147867b8b6efd88", "reference": "739b35e53379900cc9ac327b2147867b8b6efd88", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"nikic/php-parser": "^4.7", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T15:52:27+00:00"}, {"name": "sebastian/diff", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "3461e3fccc7cfdfc2720be910d3bd73c69be590d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/3461e3fccc7cfdfc2720be910d3bd73c69be590d", "reference": "3461e3fccc7cfdfc2720be910d3bd73c69be590d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:10:38+00:00"}, {"name": "sebastian/environment", "version": "5.1.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "1b5dff7bb151a4db11d49d90e5408e4e938270f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/1b5dff7bb151a4db11d49d90e5408e4e938270f7", "reference": "1b5dff7bb151a4db11d49d90e5408e4e938270f7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/5.1.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-04-03T09:37:03+00:00"}, {"name": "sebastian/exporter", "version": "4.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "ac230ed27f0f98f597c8a2b6eb7ac563af5e5b9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/ac230ed27f0f98f597c8a2b6eb7ac563af5e5b9d", "reference": "ac230ed27f0f98f597c8a2b6eb7ac563af5e5b9d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T06:03:37+00:00"}, {"name": "sebastian/global-state", "version": "5.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "0ca8db5a5fc9c8646244e629625ac486fa286bf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/0ca8db5a5fc9c8646244e629625ac486fa286bf2", "reference": "0ca8db5a5fc9c8646244e629625ac486fa286bf2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-02-14T08:28:10+00:00"}, {"name": "sebastian/lines-of-code", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "c1c2e997aa3146983ed888ad08b15470a2e22ecc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/c1c2e997aa3146983ed888ad08b15470a2e22ecc", "reference": "c1c2e997aa3146983ed888ad08b15470a2e22ecc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"nikic/php-parser": "^4.6", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-28T06:42:11+00:00"}, {"name": "sebastian/object-enumerator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/5c9eeac41b290a3712d88851518825ad78f45c71", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:12:34+00:00"}, {"name": "sebastian/object-reflector", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:14:26+00:00"}, {"name": "sebastian/recursion-context", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "cd9d8cf3c5804de4341c283ed787f099f5506172"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/cd9d8cf3c5804de4341c283ed787f099f5506172", "reference": "cd9d8cf3c5804de4341c283ed787f099f5506172", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:17:30+00:00"}, {"name": "sebastian/resource-operations", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8", "reference": "0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:45:17+00:00"}, {"name": "sebastian/type", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "fb3fe09c5f0bae6bc27ef3ce933a1e0ed9464b6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/fb3fe09c5f0bae6bc27ef3ce933a1e0ed9464b6e", "reference": "fb3fe09c5f0bae6bc27ef3ce933a1e0ed9464b6e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/3.2.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-12T14:47:03+00:00"}, {"name": "sebastian/version", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c6c1022351a901512170118436c764e473f6de8c", "reference": "c6c1022351a901512170118436c764e473f6de8c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:39:44+00:00"}, {"name": "swoole/ide-helper", "version": "4.8.12", "source": {"type": "git", "url": "https://github.com/swoole/ide-helper.git", "reference": "afe3a09f8c49a6011e2206a03e55e391d97d81b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swoole/ide-helper/zipball/afe3a09f8c49a6011e2206a03e55e391d97d81b0", "reference": "afe3a09f8c49a6011e2206a03e55e391d97d81b0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Team Swoole", "email": "<EMAIL>"}], "description": "IDE help files for Swoole.", "support": {"issues": "https://github.com/swoole/ide-helper/issues", "source": "https://github.com/swoole/ide-helper/tree/4.8.12"}, "funding": [{"url": "https://gitee.com/swoole/swoole?donate=true", "type": "custom"}, {"url": "https://github.com/swoole", "type": "github"}], "time": "2022-09-22T16:31:12+00:00"}, {"name": "symfony/event-dispatcher", "version": "v5.4.9", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc", "reference": "8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.9"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-05T16:45:39+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "f98b54df6ad059855739db6fcbc2d36995283fe1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/f98b54df6ad059855739db6fcbc2d36995283fe1", "reference": "f98b54df6ad059855739db6fcbc2d36995283fe1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/filesystem", "version": "v5.4.13", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "ac09569844a9109a5966b9438fc29113ce77cf51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/ac09569844a9109a5966b9438fc29113ce77cf51", "reference": "ac09569844a9109a5966b9438fc29113ce77cf51", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v5.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-09-21T19:53:16+00:00"}, {"name": "symfony/options-resolver", "version": "v5.4.11", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "54f14e36aa73cb8f7261d7686691fd4d75ea2690"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/54f14e36aa73cb8f7261d7686691fd4d75ea2690", "reference": "54f14e36aa73cb8f7261d7686691fd4d75ea2690", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v5.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T13:00:38+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "707403074c8ea6e2edaf8794b0157a0bfa52157a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/707403074c8ea6e2edaf8794b0157a0bfa52157a", "reference": "707403074c8ea6e2edaf8794b0157a0bfa52157a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/stopwatch", "version": "v5.4.13", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "6df7a3effde34d81717bbef4591e5ffe32226d69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/6df7a3effde34d81717bbef4591e5ffe32226d69", "reference": "6df7a3effde34d81717bbef4591e5ffe32226d69", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/service-contracts": "^1|^2|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v5.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-09-28T13:19:49+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/34a41e998c2183e22995f158c581e7b5e755ab9e", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.1"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2021-07-28T10:34:58+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=7.4", "ext-bcmath": "*", "ext-zip": "*"}, "platform-dev": [], "plugin-api-version": "2.3.0"}