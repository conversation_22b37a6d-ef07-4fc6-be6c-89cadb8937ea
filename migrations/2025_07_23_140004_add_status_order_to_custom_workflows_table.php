<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddStatusOrderToCustomWorkflowsTable extends Migration
{
    protected $connection = 'tchip_redmine';

    public function up(): void
    {
        if (Schema::hasTable('custom_workflows')) {
            Schema::table('custom_workflows', function (Blueprint $table) {
                if (!Schema::hasColumn('custom_workflows', 'status_order')) {
                    $table->integer('status_order')->default(0)->comment('状态排序')->after('template_name');
                }
            });
        }
    }

    public function down(): void
    {
        Schema::table('custom_workflows', function (Blueprint $table) {
            if (Schema::hasColumn('custom_workflows', 'status_order')) {
                $table->dropColumn('status_order');
            }
        });
    }
}