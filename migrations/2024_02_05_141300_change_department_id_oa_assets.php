<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;
use Hyperf\DbConnection\Db;
use \App\Model\TchipBi\OaAssetsManagementModel;

class ChangeDepartmentIdOaAssets extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('oa_assets')) {
            Schema::table('oa_assets', function (Blueprint $table) {
                if (Schema::hasColumn('oa_assets', 'department_id')) {
                    Db::table('oa_assets')
                        ->whereNull('department_id')
                        ->update(['department_id' => 0]);
                    $table->integer('department_id')->nullable(false)->default(0)->change();
                }
            });
        }

        if (Schema::hasTable('oa_records')) {
            Schema::table('oa_records', function (Blueprint $table) {
                if (Schema::hasColumn('oa_records', 'receiver_depart_id')) {
                    Db::table('oa_records')
                        ->whereNull('receiver_depart_id')
                        ->update(['receiver_depart_id' => 0]);
                    $table->integer('receiver_depart_id')->nullable(false)->default(0)->change();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
