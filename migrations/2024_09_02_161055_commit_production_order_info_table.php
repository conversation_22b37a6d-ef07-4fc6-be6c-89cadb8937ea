<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitProductionOrderInfoTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('production_order_info')) {
            Schema::table('production_order_info', function (Blueprint $table) {
                if (!Schema::hasColumn('production_order_info', 'product_type')) {
                    $table->string('product_type')->default('')->comment('产品类型')->after('product_platform');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_order_info', function (Blueprint $table) {
            //
        });
    }
}
