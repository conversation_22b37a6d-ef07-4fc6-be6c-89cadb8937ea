<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitBirthdaySexContactUserTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('user')) {
            Schema::table('user', function (Blueprint $table) {
                if (!Schema::hasColumn('user', 'birthday_custom')) {
                    // 添加 birthday 字段，类型为日期时间，可为空
                    $table->dateTime('birthday_custom')->nullable()->comment('生日');
                }
                if (!Schema::hasColumn('user', 'sex')) {
                    // 添加 sex 字段，类型为字符串，可为空
                    $table->string('sex')->nullable()->comment('性别');
                }
                if (!Schema::hasColumn('user', 'contact')) {
                    // 添加 contact 字段，类型为字符串，可为空
                    $table->string('contact')->nullable()->comment('联系方式');
                }
                if (!Schema::hasColumn('user', 'last_login_time')) {
                    $table->dateTime('last_login_time')->nullable()->comment('最后登录时间');
                }

            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
