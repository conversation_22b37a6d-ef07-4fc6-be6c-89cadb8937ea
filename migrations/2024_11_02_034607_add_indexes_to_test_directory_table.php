<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddIndexesToTestDirectoryTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('test_directory', function (Blueprint $table) {
            $table->index('project_id', 'idx_project_id');   // 添加 project_id 索引
            $table->index('parent_id', 'idx_parent_id');     // 添加 parent_id 索引
            $table->index('library_id', 'idx_library_id');   // 添加 library_id 索引
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('test_directory', function (Blueprint $table) {
            $table->dropIndex('idx_project_id');   // 删除 project_id 索引
            $table->dropIndex('idx_parent_id');     // 删除 parent_id 索引
            $table->dropIndex('idx_library_id');     // 删除 library_id 索引
        });
    }
}
