<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductionDataChangeDetailTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('production_data_change_detail')) {
            Schema::create('production_data_change_detail', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->integer('change_log_id')->default(0)->comment('日志id');
                $table->string('field')->default('')->comment('字段名');
                $table->string('field_text')->default('')->comment('字段描述');
                $table->string('property')->default('')->comment('字段属性');
                $table->string('property_text')->default('')->comment('字段属性描述');
                $table->text('old_value')->nullable()->comment('旧值');
                $table->text('old_value_text')->nullable()->comment('旧值描述');
                $table->text('new_value')->nullable()->comment('新值');
                $table->text('new_value_text')->nullable()->comment('新值描述');
//                $table->text('description')->nullable()->comment('描述');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index('change_log_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_data_change_detail', function (Blueprint $table) {
            //
        });
    }
}
