<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateWikiDocumentStatisticsTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (!Schema::hasTable('wiki_document_statistics')) {
                Schema::create('wiki_document_statistics', function (Blueprint $table) {
                    // 主键
                    $table->bigIncrements('stat_id')->comment('统计记录ID');

                    // 字段
                    $table->bigInteger('doc_id')->comment('关联的文档ID');
                    $table->date('date')->comment('统计日期');
                    $table->integer('view_count')->default(0)->comment('当日浏览量');
                    $table->integer('like_count')->default(0)->comment('当日点赞量');
                    $table->integer('comment_count')->default(0)->comment('当日评论量');
                    $table->dateTime('created_at')->comment('创建时间');
                    $table->dateTime('updated_at')->comment('更新时间');

                    // 索引
                    $table->index('doc_id');
                    $table->index('date');
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('wiki_document_statistics');
        }
    }
