<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitHandleUidProductTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('product')) {
            Schema::table('product', function (Blueprint $table) {
                // 处理硬件处理人字段
                if (!Schema::hasColumn('product', 'hard_handler_id')) {
                    $table->json('hard_handler_id')->nullable()->comment('硬件处理人json');
                }

                // if (Schema::hasColumn('product', 'hard_handler_uid')) {
                //     // $table->renameColumn('hard_handler_uid', 'hard_handler_uid_int')->change();
                //     $table->json('hard_handler_uid')->nullable()->comment('硬件处理人json');
                // } else {
                //     $table->json('hard_handler_uid')->nullable()->comment('硬件处理人json');
                // }

                // 处理软件处理人字段
                if (!Schema::hasColumn('product', 'soft_handler_id')) {
                    $table->json('soft_handler_id')->nullable()->comment('软件处理人json');
                }

                // if (Schema::hasColumn('product', 'soft_handler_uid')) {
                //     // $table->renameColumn('soft_handler_uid', 'soft_handler_uid_int')->change();
                //     $table->json('soft_handler_uid')->nullable()->comment('软件处理人json');
                // } else {
                //     $table->json('soft_handler_uid')->nullable()->comment('软件处理人json');
                // }
                // 添加平台字段
                if (!Schema::hasColumn('product', 'platform')) {
                    $table->char('platform', 32)->comment('产品平台');
                }

            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
