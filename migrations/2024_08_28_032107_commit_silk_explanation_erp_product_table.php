<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitSilkExplanationErpProductTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('erp_product')) {

            Schema::table('erp_product', function (Blueprint $table) {
                // 修改 silk_remark 列的注释
                if (Schema::hasColumn('erp_product', 'silk_remark')) {
                    $table->longText('silk_remark')->nullable()->comment('丝印备注')->change();
                }

                // 添加 silk_explanation 列
                if (!Schema::hasColumn('erp_product', 'silk_explanation')) {
                    $table->text('silk_explanation')->nullable()->comment('丝印解释阐明')->after('silk_remark');
                }
            });
        }
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('erp_product');
    }
}
