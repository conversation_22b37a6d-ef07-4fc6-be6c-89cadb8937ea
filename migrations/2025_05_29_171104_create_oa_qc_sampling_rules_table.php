<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOaQcSamplingRulesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('oa_qc_sampling_rules', function (Blueprint $table) {
            $table->unsignedInteger('id', true);
            $table->string('sample_code', 1)->comment('样本字码');
            $table->decimal('aql_value', 5, 2)->comment('AQL值');
            $table->integer('acceptance_num')->comment('接收数(AC)');
            $table->integer('rejection_num')->comment('拒收数(RE)');
            $table->dateTime('created_at');
            $table->dateTime('updated_at');
            $table->dateTime('deleted_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('oa_qc_sampling_rules');
    }
}
