<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddPinnedFieldsToWikiDocumentsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wiki_documents', function (Blueprint $table) {
            // 添加置顶相关字段
            $table->tinyInteger('is_pinned')->default(0)->comment('是否置顶：0=未置顶，1=已置顶')->after('sort_order');
            $table->timestamp('pinned_at')->nullable()->comment('置顶时间')->after('is_pinned');
            $table->unsignedInteger('pinned_by')->nullable()->comment('置顶操作人ID')->after('pinned_at');
            $table->string('pinned_reason', 500)->nullable()->comment('置顶原因')->after('pinned_by');
            
            // 添加索引以提高查询性能
            $table->index(['is_pinned', 'pinned_at'], 'idx_pinned_sort');
            $table->index(['space_id', 'is_pinned', 'pinned_at'], 'idx_space_pinned');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wiki_documents', function (Blueprint $table) {
            // 删除索引
            $table->dropIndex('idx_pinned_sort');
            $table->dropIndex('idx_space_pinned');
            
            // 删除字段
            $table->dropColumn(['is_pinned', 'pinned_at', 'pinned_by', 'pinned_reason']);
        });
    }
}