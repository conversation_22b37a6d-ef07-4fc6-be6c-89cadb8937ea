<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitAssembleOrderAttachmentTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('assemble_order_attachment')) {
            Schema::table('assemble_order_attachment', function (Blueprint $table) {
                if (!Schema::hasColumn('assemble_order_attachment', 'category_id')) {
                    $table->integer('category_id')->default(0)->comment('对应category表ID')->after('attachment_remark');
                }
                if (!Schema::hasColumn('assemble_order_attachment', 'category_pid')) {
                    $table->integer('category_pid')->default(0)->comment('对应category表pid')->after('category_id');
                }
                if (!Schema::hasColumn('assemble_order_attachment', 'sort')) {
                    $table->integer('sort')->default(1000)->comment('排序')->after('category_pid');
                }
                if (!Schema::hasColumn('assemble_order_attachment', 'file_type')) {
                    $table->string('file_type')->default('')->comment('文件类型')->after('sort');
                }
                if (!Schema::hasColumn('assemble_order_attachment', 'file_key')) {
                    $table->string('file_key')->default('')->comment('文件标识')->after('file_type');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_attachment', function (Blueprint $table) {
            //
        });
    }
}
