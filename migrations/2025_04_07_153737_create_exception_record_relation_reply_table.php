<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateExceptionRecordRelationReplyTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('exception_record_relation_reply')) {
            Schema::create('exception_record_relation_reply', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->tinyInteger('exception_record_id')->default(0)->comment('exception_record表id');
                $table->tinyInteger('relation_id')->default(0)->comment('exception_record_relation表id');
                $table->text('reply_content')->nullable()->comment('反馈结果');
                $table->integer('created_by')->default(0)->comment('创建人');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                $table->comment('异常记录关联反馈表');
                $table->index('relation_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exception_record_relation_reply', function (Blueprint $table) {
            //
        });
    }
}
