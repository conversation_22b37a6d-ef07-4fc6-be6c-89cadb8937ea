<?php
/*
 * @Description: 增加noticeid、noticename字段
 * @Version: 1.0
 * @Autor: 张权江
 * @Date: 2025-05-14 17:10:53
 * @LastEditors: 张权江
 * @LastEditTime: 2025-05-14 17:40:01
 */

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitNoticeidNoticenameUserNoticePush extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('user_notice_push')) {
            Schema::table('user_notice_push', function (Blueprint $table) {
                // 添加部门字段
                if (!Schema::hasColumn('user_notice_push', 'notice_id')) {
                    $table->integer('notice_id')->default(0)->comment('通知ID')->after('id');
                }
                if (!Schema::hasColumn('user_notice_push', 'notice_title')) {
                    $table->string('notice_title')->default('')->comment('通知标题')->after('notice_id');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('user_notice_push')) {
            Schema::table('user_notice_push', function (Blueprint $table) {
                if (Schema::hasColumn('user_notice_push', 'notice_id')) {
                    $table->dropColumn('notice_id');
                }
                if (Schema::hasColumn('user_notice_push', 'notice_title')) {
                    $table->dropColumn('notice_title');
                }
            });
        }
    }
}
