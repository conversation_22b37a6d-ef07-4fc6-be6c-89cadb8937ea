<?php
/*
 * @Description: 
 * @Version: 1.0
 * @Autor: 张权江
 * @Date: 2025-04-14 15:07:11
 * @LastEditors: 张权江
 * @LastEditTime: 2025-04-17 15:52:12
 */

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitDepartmentsRolesNoticeTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('notice')) {
            Schema::table('notice', function (Blueprint $table) {
                // 添加部门字段
                if (!Schema::hasColumn('notice', 'departments')) {
                    $table->json('departments')->nullable()->comment('发送部门')->after('users');
                }
                
                // 添加角色字段
                if (!Schema::hasColumn('notice', 'roles')) {
                    $table->json('roles')->nullable()->comment('发送角色')->after('departments');
                }

                //用户是否可以取消订阅
                if (!Schema::hasColumn('notice', 'is_force_subscribe')) {
                    $table->tinyInteger('is_force_subscribe')->default(0)->comment('用户是否可以取消订阅(强制订阅)')->after('roles');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('notice')) {
            Schema::table('notice', function (Blueprint $table) {
                if (Schema::hasColumn('notice', 'departments')) {
                    $table->dropColumn('departments');
                }
                if (Schema::hasColumn('notice', 'roles')) {
                    $table->dropColumn('roles');
                }
                if (Schema::hasColumn('notice', 'is_force_subscribe')) {
                    $table->dropColumn('is_force_subscribe');
                }
            });
        }
    }
}