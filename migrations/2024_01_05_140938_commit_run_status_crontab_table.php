<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitRunStatusCrontabTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('crontab')) {
            Schema::table('crontab', function (Blueprint $table) {
                if (!Schema::hasColumn('crontab', 'run_status')) {
                    // 0:未执行,1:执行完成，2:执行中
                    $table->tinyInteger('run_status')->default(0)->comment('执行状态');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
