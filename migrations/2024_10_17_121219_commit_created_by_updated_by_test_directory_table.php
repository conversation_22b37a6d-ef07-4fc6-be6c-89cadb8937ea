<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitCreatedByUpdatedByTestDirectoryTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('test_directory')) {
            Schema::table('test_directory', function (Blueprint $table) {
                if (!Schema::hasColumn('test_directory', 'created_by')) {
                    $table->integer('created_by')->nullable()->comment('创建人')->after('description');
                }
                if (!Schema::hasColumn('test_directory', 'updated_by')) {
                    $table->integer('updated_by')->nullable()->comment('更新人')->after('description');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
