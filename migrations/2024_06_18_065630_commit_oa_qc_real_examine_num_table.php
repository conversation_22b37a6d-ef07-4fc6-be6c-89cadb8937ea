<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitOaQcRealExamineNumTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('oa_qc')) {
            Schema::table('oa_qc', function (Blueprint $table) {
                if (!Schema::hasColumn('oa_qc', 'real_examine_num')) {
                    $table->integer('real_examine_num')->after('examine_num')->default(0)->comment('实际送检数量');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
