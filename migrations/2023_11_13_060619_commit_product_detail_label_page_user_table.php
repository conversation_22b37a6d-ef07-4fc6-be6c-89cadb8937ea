<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitProductDetailLabelPageUserTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user', function (Blueprint $table) {
            //
            if (!Schema::hasColumn('user', 'product_detail_label_page')) {
                $table->string('product_detail_label_page')
                    ->nullable()
                    ->default('base')
                    ->comment('base:基本信息、workProgress:工作跟进');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
