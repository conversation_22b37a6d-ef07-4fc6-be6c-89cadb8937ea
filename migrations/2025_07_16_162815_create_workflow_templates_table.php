<?php
declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateWorkflowTemplatesTable extends Migration
{
    protected $connection = 'tchip_redmine';

    public function up(): void
    {
        Schema::create('workflow_templates', function (Blueprint $table) {
            // 基础字段
            $table->bigIncrements('id');
            $table->integer('project_id')->nullable(false)->comment('所属项目ID');
            $table->string('template_name', 100)->nullable(false)->comment('模板名称');
            $table->text('description')->nullable()->comment('模板描述');
            $table->integer('created_by')->nullable()->default(null)->comment('创建人ID');

            // 自动时间戳
            $table->timestamps();
            $table->timestamp('deleted_at')->nullable()->comment('软删除时间戳');

            // 索引
            $table->index('project_id', 'idx_wf_templates_project');
            $table->unique(['project_id', 'template_name'], 'uk_wf_templates_project_name');
            $table->index('created_by', 'idx_wf_templates_created_by');
            $table->index('deleted_at', 'idx_wf_templates_deleted_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('workflow_templates');
    }
}