<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateWikiCatalogMembersTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('wiki_catalog_members')) {
            Schema::create('wiki_catalog_members', function (Blueprint $table) {
                // 主键
                $table->bigIncrements('member_id')->comment('成员ID');

                // 字段
                $table->string('associated_field', 255)
                    ->comment('主要关联字段，可为:department_id、user_id、group_id、system_role_id');
                $table->bigInteger('catalog_id')->nullable()->comment('空间ID');
                $table->bigInteger('user_id')->nullable()->comment('用户ID');
                $table->bigInteger('group_id')->nullable()->comment('组ID');
                $table->bigInteger('department_id')->nullable()->comment('关联 bi_user_department（部门 ID）');
                $table->bigInteger('system_role_id')->nullable()->comment('关联 bi_auth_group（系统角色 ID）');
                $table->string('role', 50)->nullable()->comment('成员角色: owner，editor，member，visitor');
                $table->tinyInteger('permission_level')->nullable()->comment('权限级别(1-4)');
                $table->dateTime('deleted_at')->nullable()->comment('软删除时间');
                $table->dateTime('created_at')->comment('创建时间');
                $table->dateTime('updated_at')->comment('更新时间');

                // 索引
                $table->index('catalog_id');
                $table->index('user_id');
                $table->index('group_id');
                $table->index('department_id');
                $table->index('system_role_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wiki_catalog_members');
    }
}
