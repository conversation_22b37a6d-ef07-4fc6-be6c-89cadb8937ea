<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitIsPublicIssuesExtTable extends Migration
{

    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('issues_ext')) {
            Schema::table('issues_ext', function (Blueprint $table) {

                if (!Schema::hasColumn('issues_ext', 'issue_id')) {
                    $table->bigInteger('issue_id')->default(0)->unique();
                } else {
                    // 添加索引
                    $table->unique('issue_id');
                }
                if (!Schema::hasColumn('issues_ext', 'is_public')) {
                    $table->tinyInteger('is_public')->default(0)->comment('是否公开问题');
                }

            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
