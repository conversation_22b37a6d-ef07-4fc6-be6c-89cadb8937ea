<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddIsPublicToWikiDocumentsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('wiki_documents')) {
            Schema::table('wiki_documents', function (Blueprint $table) {
                $table->tinyInteger('is_public')->default(0)->after('catalog_id')->comment('是否公开（1公开共享|0组内共享|-1仅创建者私有）');
                $table->index('is_public');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('wiki_documents')) {
            Schema::table('wiki_documents', function (Blueprint $table) {
                $table->dropIndex(['is_public']);
                $table->dropColumn('is_public');
            });
        }
    }
}