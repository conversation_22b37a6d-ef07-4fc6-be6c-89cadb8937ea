<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitRelationProjectIdIssuesExtTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('issues_ext')) {
            Schema::table('issues_ext', function (Blueprint $table) {
                $table->integer('relation_project_id')->nullable()->comment('关联项目');
                $table->integer('relation_version_id')->nullable()->comment('关联项目版本');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
