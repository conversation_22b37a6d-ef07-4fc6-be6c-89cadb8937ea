<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductBorrowDetailsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('oa_product_borrow_details')) {
            Schema::create('oa_product_borrow_details', function (Blueprint $table) {
                $table->bigIncrements('id')->unique(); // 递增主键
                $table->bigInteger('b_id')->comment('借用订单主键product_borrow.id'); // 生成bigint - 借用产品主ID，product_borrow.id
                $table->string('prod_name', 64)->comment('产品名称');; // 生成varchar - 产品名称
                $table->char('material', 16)->comment('产品料号'); // 生成char - 产品料号
                $table->smallInteger('num')->comment('借用数量'); // 生成smallint - 借用数量
                $table->tinyInteger('borrow_status')->comment('借用状态'); // 生成tinyint - 借用状态
                $table->dateTime('created_at'); // datetime
                $table->dateTime('updated_at'); // datetime
                $table->dateTime('deleted_at')->nullable(); // datetime
                // $table->softDeletes(); // 生成datetime - 软删除deleted_at
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_borrow_details', function (Blueprint $table) {
            //
        });
    }
}
