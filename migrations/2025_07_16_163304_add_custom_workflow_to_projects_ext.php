<?php
declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\ColumnDefinition;

class AddCustomWorkflowToProjectsExt extends Migration
{
    protected $connection = 'tchip_redmine';

    public function up(): void
    {
        Schema::table('projects_ext', function (Blueprint $table) {
            if (!Schema::hasColumn('projects_ext', 'use_custom_workflow')) {
                $table->boolean('use_custom_workflow')
                    ->default(false)
                    ->comment('是否启用自定义工作流');
            }
        });
    }

    public function down(): void
    {
        Schema::table('projects_ext', function (Blueprint $table) {
            if (Schema::hasColumn('projects_ext', 'use_custom_workflow')) {
                $table->dropColumn('use_custom_workflow');
            }
        });
    }
}