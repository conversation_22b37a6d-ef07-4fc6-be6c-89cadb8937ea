<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;

class CreateDocumentStatusTypesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wiki_document_status_types', function (Blueprint $table) {
            $table->string('status_type', 50)->primary()->comment('状态类型标识');
            $table->string('description', 200)->comment('业务含义描述');
            $table->tinyInteger('is_active')->default(1)->comment('是否启用 1:启用 0:禁用');
            $table->dateTime('created_at')->nullable()->comment('创建时间');
            $table->dateTime('updated_at')->nullable()->comment('更新时间');
            
            $table->comment('文档状态类型枚举表');
        });
        
        // 插入默认状态类型
        DB::table('wiki_document_status_types')->insert([
            [
                'status_type' => 'premium',
                'description' => '精华认证（文档被标记为精华）',
                'is_active' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'status_type' => 'training',
                'description' => '培训认证（文档被官方认证为培训材料）',
                'is_active' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'status_type' => 'audit',
                'description' => '审核状态（文档是否通过审核）',
                'is_active' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wiki_document_status_types');
    }
}