<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateAssembleOrderMaterialCompletenessTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('assemble_order_material_completeness')) {
            Schema::create('assemble_order_material_completeness', function (Blueprint $table) {
                $table->integerIncrements('id')->comment('id');
                $table->integer('assemble_order_id')->comment('组装订单id');
                $table->tinyInteger('type')->default(0)->comment('材料类型');
                $table->tinyInteger('status')->default(1)->comment('齐料状态');
                $table->json('attachment_ids')->nullable()->comment('附件');
                $table->text('remark')->nullable()->comment('备注');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间');
                #建立索引
                $table->index('assemble_order_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_material_completeness', function (Blueprint $table) {
            //
        });
    }
}
