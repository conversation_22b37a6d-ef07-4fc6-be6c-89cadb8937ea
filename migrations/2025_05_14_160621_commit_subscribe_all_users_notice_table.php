<?php
/*
 * @Description: 增加是否订阅所有用户字段
 * @Version: 1.0
 * @Autor: 张权江
 * @Date: 2025-05-14 16:06:21
 * @LastEditors: 张权江
 * @LastEditTime: 2025-05-14 17:09:28
 */

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitSubscribeAllUsersNoticeTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('notice')) {
            Schema::table('notice', function (Blueprint $table) {
                // 添加部门字段
                if (!Schema::hasColumn('notice', 'subscribe_all_users')) {
                    $table->tinyInteger('subscribe_all_users')->default(0)->comment('是否订阅所有用户')->after('users');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('notice')) {
            Schema::table('notice', function (Blueprint $table) {
                if (Schema::hasColumn('notice', 'subscribe_all_users')) {
                    $table->dropColumn('subscribe_all_users');
                }
            });
        }
    }
}
