<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ChangeProductionOrderInfoTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('production_order_info')) {
            Schema::table('production_order_info', function (Blueprint $table) {
                //修改hardware_user_id的注释为原理图负责人
                if (Schema::hasColumn('production_order_info', 'hardware_user_id')) {
                    $table->unsignedInteger('hardware_user_id')->comment('原理图负责人')->change();
                }
                if (!Schema::hasColumn('production_order_info', 'layout_user_id')) {
                    $table->unsignedInteger('layout_user_id')->default(0)->comment('布局负责人')->after('hardware_user_id');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_order_info', function (Blueprint $table) {
            //
        });
    }
}
