<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateMarketingProductTable extends Migration
{
    /**
     * Run the migrations.
     * 产品销售推广汇总表
     */
    public function up(): void
    {
        if (!Schema::hasTable('marketing_product_month_report')) {
            Schema::create('marketing_product_month_report', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('brand_name', 32);
                $table->char('brand_code', 16)->comment('品牌的code');
                $table->integer('product_id')->comment('产品ID');
                $table->date('date')->comment('日期');
                $table->decimal('amount_quota', 12)->default(0)->comment('销售额目标');
                $table->integer('volume_quota')->default(0)->comment('销售量目标');
                $table->decimal('amount', 12)->default(0)->comment('销售额');
                $table->decimal('rmb', 12)->default(0)->comment('销售额人民币');
                $table->decimal('dollar', 12)->default(0)->comment('销售额美元');
                $table->integer('volume')->default(0)->comment('销售量');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketing_product');
    }
}
