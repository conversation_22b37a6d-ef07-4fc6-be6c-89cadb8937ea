<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitVersionProductProgress extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_progress', function (Blueprint $table) {
            if (!Schema::hasColumn('product_progress', 'version')) {
                $table->string('version', 20)->nullable()->comment('版本号');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_progress', function (Blueprint $table) {
            //
        });
    }
}
