<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductionOrderChangeMaterialTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('production_order_change_material')) {
            Schema::create('production_order_change_material', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('production_order_id')->default(0)->comment('生产订单id');
                $table->string('old_product_code')->default('')->comment('旧料号');
                $table->string('old_product_name')->default('')->comment('旧名称');
                $table->string('old_product_spec')->default('')->comment('旧规格');
                $table->string('new_product_code')->default('')->comment('新料号');
                $table->string('new_product_name')->default('')->comment('新名称');
                $table->string('new_product_spec')->default('')->comment('新规格');
                $table->string('description')->default('')->comment('描述');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index('production_order_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_order_change_material');
    }
}
