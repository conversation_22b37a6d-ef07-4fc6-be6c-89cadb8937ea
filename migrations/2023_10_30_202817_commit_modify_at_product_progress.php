<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitModifyAtProductProgress extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_progress', function (Blueprint $table) {
            if (!Schema::hasColumn('product_progress', 'modify_at')) {
                $table->date('modify_at')->nullable()->comment('打样或下单时间');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_progress', function (Blueprint $table) {
            //
        });
    }
}
