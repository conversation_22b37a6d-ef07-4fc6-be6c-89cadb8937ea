<?php

use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Schema\Schema;

class ModifyAssembleOrderInfoTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('assemble_order_info')) {
            Schema::table('assemble_order_info', function (Blueprint $table) {
                if (Schema::hasColumn('assemble_order_info', 'start_sn_no')) {
                    $table->dropColumn('start_sn_no');
                }
                if (Schema::hasColumn('assemble_order_info', 'start_mac_address')) {
                    $table->dropColumn('start_mac_address');
                }
                if (Schema::hasColumn('assemble_order_info', 'mac_address_attachment_id')) {
                    $table->dropColumn('mac_address_attachment_id');
                }
                if (Schema::hasColumn('assemble_order_info', 'sn_no_attachment_id')) {
                    $table->dropColumn('sn_no_attachment_id');
                }
                if (!Schema::hasColumn('assemble_order_info', 'mac_sn_attachment_id')) {
                    $table->integer('mac_sn_attachment_id')->default(0)->comment('MAC SN附件ID');
                }
                if (!Schema::hasColumn('assemble_order_info', 'mac_text_origin_type')) {
                    $table->tinyInteger('mac_text_origin_type')->default(1)->comment('MAC来源类型 1天启 2客户');
                }
                if (!Schema::hasColumn('assemble_order_info', 'sn_text_origin_type')) {
                    $table->tinyInteger('sn_text_origin_type')->default(1)->comment('Sn来源类型 1天启 2客户');
                }
                if (!Schema::hasColumn('assemble_order_info','sn_remark')) {
                    $table->text('sn_remark')->nullable()->comment('整机SN文本');
                }
                if(Schema::hasColumn('assemble_order_info','pcba_version')) {
                    $table->dropColumn('pcba_version');
                }
                if (!Schema::hasColumn('assemble_order_info','pcba_remark')) {
                    $table->text('pcba_remark')->nullable()->comment('PCBA备注');
                }
                if (!Schema::hasColumn('assemble_order_info', 'predict_delivery_date')) {
                    $table->date('predict_delivery_date')->nullable()->comment('预定交货时间')->after('is_complete');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_info', function (Blueprint $table) {
            //
        });
    }
}
