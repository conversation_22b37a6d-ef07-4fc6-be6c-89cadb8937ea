<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitRelativeIdTestCaseChangeTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('test_case_change')) {
            Schema::table('test_case_change', function (Blueprint $table) {
                if (!Schema::hasColumn('test_case_change', 'relative_id')) {
                    $table->integer('relative_id')->nullable()->comment('相关ID')->after('test_case_id');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
