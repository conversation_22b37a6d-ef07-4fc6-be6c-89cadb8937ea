<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOaQcInspectionLevelsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('oa_qc_inspection_levels', function (Blueprint $table) {
            $table->unsignedInteger('id', true);
            $table->string('level_code', 20)->unique()->comment('检查水平代码(如:Special-1,Normal-2)');
            $table->string('level_name', 50)->comment('检查水平名称');
            $table->string('standard_code', 10)->nullable()->comment('标准中的原始代码(如:S-1,Ⅰ)');
            $table->dateTime('created_at');
            $table->dateTime('updated_at');
            $table->dateTime('deleted_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('oa_qc_inspection_levels');
    }
}
