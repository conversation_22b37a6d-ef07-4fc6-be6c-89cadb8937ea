<?php

use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Schema\Schema;

class CommitProductionOuthelpRecordTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('production_outhelp_record')) {
            Schema::table('production_outhelp_record', function (Blueprint $table) {
                if (!Schema::hasColumn('production_outhelp_record', 'online_time')) {
                    $table->date('online_time')->nullable()->comment('上线时间')->after('last');
                }
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_outhelp_record', function (Blueprint $table) {
            //
        });
    }
}
