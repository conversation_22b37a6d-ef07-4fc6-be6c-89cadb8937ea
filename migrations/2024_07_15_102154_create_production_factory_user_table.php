<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductionFactoryUserTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('production_factory_user', function (Blueprint $table) {
            //
        });
        if (!Schema::hasTable('production_factory_user')) {
            Schema::create('production_factory_user', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('factory_id')->default(0)->comment('工厂');
                $table->integer('user_id')->default(0)->comment('用户');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index(['factory_id','user_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_factory_user');
    }
}
