<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOaQcBatchSampleMappingTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('oa_qc_batch_sample_mapping', function (Blueprint $table) {
            $table->unsignedInteger('id', true);
            $table->unsignedInteger('inspection_level_id')->comment('检查水平ID');
            $table->integer('min_num')->comment('最少数');
            $table->integer('max_num')->comment('最大数');
            $table->string('sample_code', 1)->comment('样本字码');
            $table->integer('sample_size')->comment('抽样数');
            $table->dateTime('created_at');
            $table->dateTime('updated_at');
            $table->dateTime('deleted_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('oa_qc_batch_sample_mapping');
    }
}
