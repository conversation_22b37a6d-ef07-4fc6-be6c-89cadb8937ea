<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ChangeAssembleOrderMaterialCompletenessTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('assemble_order_material_completeness')) {
            Schema::table('assemble_order_material_completeness', function (Blueprint $table) {
                if (!Schema::hasColumn('assemble_order_material_completeness', 'audit_status')) {
                    $table->tinyInteger('audit_status')->default(1)->comment('审核状态{1:待审核,2:已审核,3:未通过}');
                }
                if (!Schema::hasColumn('assemble_order_material_completeness', 'audit_user_id')) {
                    $table->integer('audit_user_id')->default(0)->comment('审核人');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_material_completeness', function (Blueprint $table) {
            //
        });
    }
}
