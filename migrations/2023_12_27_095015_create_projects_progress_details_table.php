<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProjectsProgressDetailsTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects_progress_details', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('progress_id')->comment('跟进主ID');
            $table->char('property', 32)->comment('类型');
            $table->string('prop_key', 64)->comment('修改的字段');
            $table->text('old_value')->comment('旧值');
            $table->text('value')->comment('新值');;
            $table->dateTime('created_at');
            $table->dateTime('updated_at');
            $table->dateTime('deleted_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects_progress_details');
    }
}
