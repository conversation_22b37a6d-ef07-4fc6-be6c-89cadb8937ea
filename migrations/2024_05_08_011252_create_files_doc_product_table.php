<?php
    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateFilesDocProductTable extends Migration
    {
        protected $connection = 'tchip_redmine';

        /**
         * Run the migrations.
         */
        public function up(): void
        {
            Schema::create('files_doc_product', function (Blueprint $table) {
                $table->bigIncrements('id')->comment('产品文件ID');
                $table->bigInteger('product_id')->nullable()->comment('产品id');
                $table->bigInteger('attachment_id')->nullable()->comment('对应附件表ID');
                $table->bigInteger('category_id')->nullable()->comment('对应category表ID');
                $table->bigInteger('category_pid')->nullable()->comment('对应category表pid');
                $table->string('sort')->nullable()->comment('用作排序');
                $table->string('file_type')->nullable()->comment('文件类型');
                $table->integer('responsible_person')->nullable()->comment('负责人');
                $table->integer('file_version')->nullable()->comment('文件版本');
                $table->integer('file_attribute')->nullable()->comment('文件属性');
                $table->string('file_status')->nullable()->comment('文件状态');
//                $table->string('file_type_custom')->nullable()->comment('自定义文件类型');
                $table->timestamps();
            });
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('files_doc_product');
        }
    }

