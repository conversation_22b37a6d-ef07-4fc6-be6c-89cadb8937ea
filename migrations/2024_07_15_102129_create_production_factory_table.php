<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductionFactoryTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('production_factory')) {
            Schema::create('production_factory', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('code')->default('')->comment('编号');
                $table->string('name')->default('')->comment('名称');
                $table->integer('manager_user_id')->default(0)->comment('负责人');
                $table->tinyInteger('status')->default(0)->comment('状态');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index('code');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_factory');
    }
}
