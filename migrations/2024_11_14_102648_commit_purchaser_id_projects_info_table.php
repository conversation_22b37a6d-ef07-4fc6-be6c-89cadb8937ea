<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitPurchaserIdProjectsInfoTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('projects_info')) {
            Schema::table('projects_info', function (Blueprint $table) {
                if (!Schema::hasColumn('projects_info', 'purchaser_id')) {
                    $table->json('purchaser_id')->nullable(true)->comment('采购负责人json格式')->after('soft_handler_uid');
                }
                if (!Schema::hasColumn('projects_info', 'architect_id')) {
                    $table->json('architect_id')->nullable(true)->comment('结构负责人json格式')->after('purchaser_id');
                }
                if (!Schema::hasColumn('projects_info', 'series_id')) {
                    $table->json('series_id')->nullable(true)->comment('产品系列json格式')->after('architect_id');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
