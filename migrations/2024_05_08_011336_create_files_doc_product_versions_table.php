<?php
    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateFilesDocProductVersionsTable extends Migration
    {
        protected $connection = 'tchip_redmine';

        /**
         * Run the migrations.
         */
        public function up(): void
        {
            Schema::create('files_doc_product_versions', function (Blueprint $table) {
                $table->bigIncrements('id')->comment('版本ID');
                $table->string('version_name')->nullable()->comment('版本名称');
                $table->string('status')->nullable()->comment('版本状态');
                $table->string('product_id')->nullable()->comment('产品id');
                $table->timestamps();
                $table->softDeletes();
            });
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('files_doc_product_versions');
        }
    }
