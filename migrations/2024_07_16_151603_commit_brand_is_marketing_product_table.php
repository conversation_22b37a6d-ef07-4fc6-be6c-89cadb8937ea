<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitBrandIsMarketingProductTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('product')) {
            Schema::create('product', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('product_name', 128);
                $table->string('product_sale_name', 128)->nullable()->comment('在销售系统中显示的名称');
                $table->char('brand', 16)->comment('品牌');
                $table->tinyInteger('is_marketing')->default(0)->comment('是否在营销显示');
                $table->smallInteger('type')->default(0)->comment('类型');
                $table->tinyInteger('status')->default(1)->comment('状态');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        } else {
            Schema::table('product', function (Blueprint $table) {
                if (!Schema::hasColumn('brand', 'product')) {
                    $table->char('brand', 16)->comment('品牌');
                }
                if (!Schema::hasColumn('is_marketing', 'product')) {
                    $table->tinyInteger('is_marketing')->default(0)->comment('是否在营销显示');
                }
                if (!Schema::hasColumn('product_sale_name', 'product')) {
                    $table->string('product_sale_name', 128)->nullable()->comment('在销售系统中显示的名称');
                }
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
