<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductionOrderAttachmentTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('production_order_attachment')) {
            Schema::create('production_order_attachment', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('production_order_id')->default(0)->comment('订单id');
                $table->integer('attachment_id')->default(0)->comment('附件id');
                $table->string('attachment_remark')->default('')->comment('附件链接或相关地址');
                $table->integer('category_id')->default(0)->comment('对应category表ID');
                $table->integer('category_pid')->default(0)->comment('对应category表pid');
                $table->integer('sort')->default(1000)->comment('排序');
                $table->string('file_type')->default('')->comment('文件类型');
                $table->string('file_key')->default('')->comment('文件标识');
                $table->tinyInteger('is_required')->default(1)->comment('是否需要');
                $table->integer('upload_user_id')->default(0)->comment('上传用户');
                $table->integer('audit_user_id')->default(0)->comment('审核用户');
                $table->tinyInteger('audit_status')->default(0)->comment('审核状态{0:待审核,1:已审核,2:未通过}');
                $table->dateTime('audit_time')->nullable()->comment('审核时间');
                $table->string('reject_reason')->default('')->comment('驳回理由');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index('production_order_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_order_attachment');
    }
}
