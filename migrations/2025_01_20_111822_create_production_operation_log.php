<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductionOperationLog extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('production_operation_log')) {
            Schema::create('production_operation_log', function (Blueprint $table) {
                $table->integerIncrements('id');

                $table->tinyInteger('type')->default(1)->comment('日志类型，1:接口日志；2:任务日志');
                $table->tinyInteger('order_type')->default(0)->comment('订单类型');
                $table->json('order_ids')->nullable()->comment('订单id');
                $table->string('qid')->default('')->comment('对应接口请求的qid');
                $table->integer('user_id')->default(0)->comment('用户id');
                $table->string('url',3000)->default('')->comment('请求地址');
                $table->string('method')->default('')->comment('请求方式');
                $table->json('request_data')->nullable()->comment('请求数据');
                $table->json('response_data')->nullable()->comment('响应数据');
                $table->integer('crontab_id')->default(0)->comment('任务名称');
                $table->string('crontab_name')->default('')->comment('任务名称');
                $table->tinyInteger('crontab_status')->default(0)->comment('任务状态');
                $table->json('crontab_data')->nullable()->comment('任务相关数据');
                $table->json('crontab_result')->nullable()->comment('任务相关结果');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index('user_id');
                $table->index('order_ids');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_operation_log', function (Blueprint $table) {
            //
        });
    }
}
