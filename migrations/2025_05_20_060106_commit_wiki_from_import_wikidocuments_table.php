<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitWikiFromImportWikidocumentsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wiki_documents', function (Blueprint $table) {
            if (!Schema::hasColumn('wiki_documents', 'wiki_from_import')) {
                $table->tinyInteger('wiki_from_import')->default(0)->comment('是否来自于导入的wiki：0否，1是')->after(
                    'current_editor_type'
                );
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('import_wikidocuments', function (Blueprint $table) {
            if (Schema::hasColumn('import_wikidocuments', 'wiki_from_import')) {
                $table->dropColumn('wiki_from_import');
            }
        });
    }
}
