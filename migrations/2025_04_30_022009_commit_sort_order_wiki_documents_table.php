<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitSortOrderWikiDocumentsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('wiki_documents') && !Schema::hasColumn('wiki_documents', 'sort_order')) {
            Schema::table('wiki_documents', function (Blueprint $table) {
                if (!Schema::hasColumn('wiki_documents', 'sort_order')) {
                    $table->integer('sort_order')->default(0)->comment('排序')->after('doc_id');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
