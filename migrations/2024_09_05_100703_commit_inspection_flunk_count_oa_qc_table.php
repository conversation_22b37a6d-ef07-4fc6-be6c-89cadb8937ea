<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitInspectionFlunkCountOaQcTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('oa_qc')) {
            Schema::table('oa_qc', function (Blueprint $table) {
                // 添加 inspection_flunk_count 列
                if (!Schema::hasColumn('oa_qc', 'inspection_flunk_count')) {
                    $table->tinyInteger('inspection_flunk_count')->default(1)
                        ->comment('同一个料号检验结果合格状态的改变记录，由5至1，根据前一次为‘不合格’置5，后续多次（合格）逐步递减；前一次再遇‘不合格’重置5')->after('status_change_at');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
