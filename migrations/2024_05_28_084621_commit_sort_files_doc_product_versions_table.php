<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitSortFilesDocProductVersionsTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('files_doc_product_versions')) {
        Schema::table('files_doc_product_versions', function (Blueprint $table) {
            if (!Schema::hasColumn('files_doc_product_versions', 'sort')) {
                $table->integer('sort')->default(0)->nullable()->comment('产品文件版本排序');
            }
        });
    }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
