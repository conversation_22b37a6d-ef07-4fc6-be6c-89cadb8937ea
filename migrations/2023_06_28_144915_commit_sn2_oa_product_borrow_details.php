<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitSn2OaProductBorrowDetails extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oa_product_borrow_details', function (Blueprint $table) {
            if (Schema::hasColumn('oa_product_borrow_details', 'borrow_sn')) {
                $table->string('borrow_sn', 32)->nullable()->change();
            }
            if (Schema::hasColumn('oa_product_borrow_details', 'return_sn')) {
                $table->string('return_sn', 32)->nullable()->change();
            }
            // if (Schema::hasColumn('oa_product_borrow_details', 'is_apply_return')) {
            //     $table->tinyInteger('is_apply_return')->change();
            // }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('oa_product_borrow_details', function (Blueprint $table) {
            //
        });
    }
}
