<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateUserPointStatisticsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_point_statistics', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->date('stat_date')->comment('统计日期');
            $table->enum('stat_type', ['daily', 'weekly', 'monthly', 'yearly'])->comment('统计类型');
            $table->integer('points_earned')->default(0)->comment('获得积分');
            $table->integer('points_consumed')->default(0)->comment('消费积分');
            $table->integer('net_points')->default(0)->comment('净积分');
            $table->integer('article_published')->default(0)->comment('发布文档数');
            $table->integer('likes_received')->default(0)->comment('获得点赞数');
            $table->integer('comments_made')->default(0)->comment('评论数');
            $table->integer('documents_viewed')->default(0)->comment('浏览文档数');
            $table->integer('active_days')->default(0)->comment('活跃天数');
            $table->integer('rank_in_period')->default(0)->comment('期间排名');
            $table->integer('achievements_unlocked')->default(0)->comment('解锁成就数');
            $table->tinyInteger('level_at_period_start')->default(1)->comment('期间开始时等级');
            $table->tinyInteger('level_at_period_end')->default(1)->comment('期间结束时等级');
            $table->timestamps();

            $table->unique(['user_id', 'stat_date', 'stat_type'], 'uk_user_date_type');
            $table->index('stat_date', 'idx_stat_date');
            $table->index('stat_type', 'idx_stat_type');
            $table->index('points_earned', 'idx_points_earned');
            $table->index('rank_in_period', 'idx_rank_in_period');
        });

        \Hyperf\DbConnection\Db::statement("ALTER TABLE `bi_user_point_statistics` comment '积分系统-用户积分统计表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_point_statistics');
    }
}