<?php

    use App\Constants\DataBaseCode;
    use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;
    use Hyperf\DbConnection\Db;

    class CreateChecklistFieldSettingsTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('checklist_field_settings', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('字段设置主键，自增');
            $table->integer('project_id')->nullable()->comment('项目ID');
            $table->string('field_name', 100)->comment('字段名（与数据库对应，例如title、assignee、或者custom_fields.due_date）');
            $table->string('display_name', 100)->comment('前端展示名称');
            $table->string('field_type', 50)->default('text')
                ->comment('字段类型，支持：text=单行文本，textarea=多行文本，select=单选下拉，
                multi_select=多选下拉，radio=单选框，checkbox=多选框，user_select=用户选择，date=日期，file=上传文件');
            $table->tinyInteger('is_enabled')->default(1)->comment('是否启用（0=不展示；1=展示）');
            $table->integer('sort_order')->default(1)->comment('展示顺序，数值越小越靠前');
            $table->dateTime('created_at')->useCurrent()->comment('创建时间');
            $table->dateTime('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->dateTime('deleted_at')->nullable()->comment('软删除时间');

            $table->index('project_id');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('checklist_field_settings');
    }
}
