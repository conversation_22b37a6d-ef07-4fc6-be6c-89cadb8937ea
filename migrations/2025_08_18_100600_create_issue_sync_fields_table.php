<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateIssueSyncFieldsTable extends Migration
{
    protected $connection = 'tchip_redmine';

    public function up(): void
    {
        if (!Schema::connection($this->connection)->hasTable('issue_sync_fields')) {
            Schema::connection($this->connection)->create('issue_sync_fields', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->bigInteger('relation_id')->nullable()->comment('关联的同步关系ID，null表示全局配置');
                $table->string('field_key', 100)->comment('需要同步的字段键名');
                $table->string('field_name', 100)->comment('字段显示名称');
                $table->string('sync_strategy', 50)->default('overwrite')->comment('同步策略: overwrite|merge|skip_if_exists');
                $table->tinyInteger('is_enabled')->default(1)->comment('是否启用该字段同步');
                $table->integer('sort_order')->default(0)->comment('排序顺序');
                $table->json('field_config')->nullable()->comment('字段额外配置JSON');
                $table->text('description')->nullable()->comment('字段说明');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();

                $table->unique(['relation_id', 'field_key'], 'idx_relation_field_unique');
                $table->index('field_key', 'idx_field_key');
                $table->index('is_enabled', 'idx_is_enabled');
            });
        }
    }

    public function down(): void
    {
        if (Schema::connection($this->connection)->hasTable('issue_sync_fields')) {
            Schema::connection($this->connection)->drop('issue_sync_fields');
        }
    }
}
