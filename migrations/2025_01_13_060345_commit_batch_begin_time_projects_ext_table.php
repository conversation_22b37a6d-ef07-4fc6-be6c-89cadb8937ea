<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitBatchBeginTimeProjectsExtTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects_ext', function (Blueprint $table) {
            if (Schema::hasTable('projects_ext')) {
                Schema::table('projects_ext', function (Blueprint $table) {
                    if (!Schema::hasColumn('projects_ext', 'batch_begin_time')) {
                        $table->dateTime('batch_begin_time')->nullable()->comment('上线时间（大批量开始时间）')->after('icon');
                    }
                });
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
