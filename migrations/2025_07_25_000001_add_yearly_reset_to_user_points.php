<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

/**
 * 添加年积分重置字段到用户积分表
 */
class AddYearlyResetToUserPoints extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_points', function (Blueprint $table) {
            $table->date('last_yearly_reset')->nullable()->comment('年积分最后重置日期')->after('last_monthly_reset');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_points', function (Blueprint $table) {
            $table->dropColumn('last_yearly_reset');
        });
    }
}