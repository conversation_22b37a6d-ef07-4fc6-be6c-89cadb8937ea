<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProjectLabelMapTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_label_map', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('field_name', 255)->nullable()->comment('字段名称');
            $table->string('from', 100)->nullable()->comment('来源于');
            $table->integer('label_id')->default(0)->comment('标签id');
            $table->integer('field_value')->default(0)->comment('字段值');
            $table->integer('created_by')->default(0)->comment('创建人');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_label_map');
    }
}
