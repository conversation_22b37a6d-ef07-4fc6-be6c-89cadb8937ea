<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitAssociatedFieldWikiSpaceMembersTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wiki_space_members', function (Blueprint $table) {
            if (Schema::hasTable('wiki_space_members')) {
                Schema::table('wiki_space_members', function (Blueprint $table) {
                    // 修改 space_id 为可为空
                    $table->bigInteger('space_id')->nullable()->change();

                    if (!Schema::hasColumn('wiki_space_members', 'associated_field')) {
                        $table->string('associated_field', 255)
                            ->comment('主要关联字段，可为:department_id、user_id、group_id、system_role_id')
                            ->after('member_id');
                    }
                });
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
