<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateTestPlanCaseResultTable extends Migration
    {
        /**
         * 指定数据库连接
         */
        protected $connection = 'tchip_redmine';

        /**
         * Run the migrations.
         */
        public function up(): void
        {
            Schema::create('test_plan_case_result', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('test_plan_case_id')->default(0)->comment('计划用例ID');
                $table->tinyInteger('test_status')->default(1)->comment('测试状态');
                $table->text('remark')->nullable()->comment('注释');
                $table->text('remark_html')->nullable()->comment('注释html');
                $table->json('detail')->nullable()->comment('操作详情');
                $table->integer('created_by')->default(0)->comment('操作人员');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间');
                //索引
                $table->index('test_plan_case_id');
            });
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('test_plan_case_result');
        }
    }
