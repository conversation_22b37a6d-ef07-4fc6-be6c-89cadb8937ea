<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductionOuthelpRecordTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_outhelp_record', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('production_order_id');
            $table->string('code')->default('')->comment('生产订单编号');
            $table->string('pcb', 256)->default('')->comment('(物料)pcb空板');
            $table->string('paster', 256)->default('')->comment('(物料)贴片料');
            $table->string('solder', 256)->default('')->comment('(物料)后焊料');
            $table->text('return_remark')->nullable(true)->comment('回货备注');
            $table->tinyInteger('last')->default(1)->comment('是否最后一次记录');
            $table->integer('user_id')->default(0)->comment('记录员');
            $table->dateTime('created_at');
            $table->dateTime('updated_at');
            $table->dateTime('deleted_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_outhelp_record');
    }
}
