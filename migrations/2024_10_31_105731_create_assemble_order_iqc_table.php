<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateAssembleOrderIqcTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('assemble_order_iqc')) {
            Schema::create('assemble_order_iqc', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->integer('assemble_order_id')->default(0)->comment('生产订单id');
                $table->integer('oa_qc_id')->default(0)->comment('oa_qc表id');
                $table->text('remark')->nullable()->comment('备注');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index('assemble_order_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_iqc', function (Blueprint $table) {
            //
        });
    }
}
