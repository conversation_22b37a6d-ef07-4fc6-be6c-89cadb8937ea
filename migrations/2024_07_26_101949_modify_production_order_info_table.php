<?php

use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Schema\Schema;

class ModifyProductionOrderInfoTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('production_order_info')) {
            Schema::table('production_order_info', function (Blueprint $table) {
                if (!Schema::hasColumn('production_order_info', 'cc_user_ids')) {
                    $table->json('cc_user_ids')->nullable()->comment('抄送人')->after('work_status_id');
                }
                if (!Schema::hasColumn('production_order_info', 'product_platform')) {
                    $table->string('product_platform')->default('')->comment('产品平台')->after('cc_user_ids');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_order_info', function (Blueprint $table) {
            //
        });
    }
}
