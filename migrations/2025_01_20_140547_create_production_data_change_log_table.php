<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductionDataChangeLogTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('production_data_change_log')) {
            Schema::create('production_data_change_log', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->integer('operation_log_id')->default(0)->comment('日志id');
                $table->tinyInteger('order_type')->default(0)->comment('订单类型');
                $table->integer('order_id')->default(0)->comment('订单id');
                $table->string('operation_type')->default('')->comment('数据操作类型');
                $table->string('table_name')->default('')->comment('表名');
                $table->string('change_category')->default('')->comment('变更类型');
                $table->string('change_category_tag')->default('')->comment('变更类型附带标签');
                $table->integer('table_row_id')->default(0)->comment('表数据行对应id');
                $table->string('model')->default('')->comment('模型');
                $table->json('old_value')->nullable()->comment('旧值');
                $table->json('new_value')->nullable()->comment('新值');
                $table->json('dirty')->nullable()->comment('变更字段');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index('operation_log_id');
                $table->index(['order_type','order_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_data_change_log', function (Blueprint $table) {
            //
        });
    }
}
