<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateMarketingPlatformTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('marketing_platform')) {
            Schema::create('marketing_platform', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('name', 128);
                $table->string('sales_name', 128)->comment('销售系统中显示的名称');
                $table->smallInteger('type')->default(0)->comment('平台类型 1:销售平台 2:推广平台');
                $table->tinyInteger('status')->default(1);
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketing_platform');
    }
}
