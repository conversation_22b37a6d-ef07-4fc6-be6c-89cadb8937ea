<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitExtOptionErpWarehouseTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('erp_warehouse')) {
            Schema::table('erp_warehouse', function (Blueprint $table) {
                if (! Schema::hasColumn('erp_warehouse', 'ext_option')) {
                    $table->json('ext_option')->nullable()->comment('扩展开关');
                }
            });

        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
