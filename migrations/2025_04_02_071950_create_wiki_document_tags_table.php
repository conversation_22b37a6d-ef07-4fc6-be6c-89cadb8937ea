<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateWikiDocumentTagsTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (!Schema::hasTable('wiki_document_tags')) {
                Schema::create('wiki_document_tags', function (Blueprint $table) {
                    // 主键
                    $table->bigIncrements('id')->comment('关联ID');

                    // 字段
                    $table->bigInteger('doc_id')->comment('文档ID');
                    $table->bigInteger('tag_id')->comment('标签ID');
                    $table->dateTime('deleted_at')->nullable()->comment('软删除时间');
                    $table->dateTime('created_at')->comment('创建时间');
                    $table->dateTime('updated_at')->comment('更新时间');

                    // 索引
                    $table->index('doc_id');
                    $table->index('tag_id');
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('wiki_document_tags');
        }
    }
