<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitProdSpecProductBorrowDetailsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oa_product_borrow_details', function (Blueprint $table) {
            $table->string('prod_spec', 128)->comment('产品规格')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('oa_product_borrow_details', function (Blueprint $table) {
            //
        });
    }
}
