<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddContentUpdatedFieldsToWikiDocumentsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wiki_documents', function (Blueprint $table) {
            $table->integer('content_updated_by')->nullable()->after('updated_by')->comment('内容更新人（用户ID）');
            $table->timestamp('content_updated_at')->nullable()->after('updated_at')->comment('内容更新时间');
        });

        // 将现有的 updated_by 和 updated_at 数据拷贝到新字段作为初始值
        \Hyperf\DbConnection\Db::statement('
            UPDATE bi_wiki_documents 
            SET content_updated_by = updated_by, 
                content_updated_at = updated_at 
            WHERE content_updated_by IS NULL 
            AND content_updated_at IS NULL
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wiki_documents', function (Blueprint $table) {
            $table->dropColumn(['content_updated_by', 'content_updated_at']);
        });
    }
}