<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitUsernameVersionProjectsProgressTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::table('projects_progress', function (Blueprint $table) {
            $table->bigIncrements('id')->change();
            $table->smallInteger('type')->default(0)->comment('跟进类型');
            $table->string('username', 16)->charset('utf8mb4')
                ->collation('utf8mb4_bin')->default('')->comment('销售系统中用户名称');
            $table->string('version_pre', 32)->nullable()->comment('旧版本');
            $table->string('version', 32)->nullable()->comment('版本');
            $table->text('describe')->nullable()->comment('改版描述');
            $table->json('attachment_reversion')->nullable()->comment('附件');
            $table->renameColumn('parent_id', 'pid')->change();
        });

        Schema::table('projects_progress', function (Blueprint $table) {
            $table->integer('pid')->nullable(false)->default(0)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
