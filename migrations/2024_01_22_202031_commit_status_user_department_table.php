<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitStatusUserDepartmentTable extends Migration
{

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_department', function (Blueprint $table) {
            $table->tinyInteger('status')->default(0)->comment('状态');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
