<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitOrderFactoryCodeOaQcTable extends Migration
{ 
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oa_qc', function (Blueprint $table) {
            if (!Schema::hasColumn('oa_qc', 'order_factory_code')) {
                // 供应商编号
                $table->string('order_factory_code', 20)->nullable()->comment('供应商编号')->after('followers');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('oa_qc', function (Blueprint $table) {
            $table->dropColumn([
                'order_factory_code', 
            ]);
        });
    }
}