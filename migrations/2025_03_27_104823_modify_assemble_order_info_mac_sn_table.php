<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ModifyAssembleOrderInfoMacSnTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('assemble_order_info')) {
            Schema::table('assemble_order_info', function (Blueprint $table) {
                if (!Schema::hasColumn('assemble_order_info', 'mac_range_origin_type')) {
                    $table->tinyInteger('mac_range_origin_type')->default(1)->comment('类型,{1:天启2:客户}');
                }
                if (!Schema::hasColumn('assemble_order_info', 'mac_address_range')) {
                    $table->json('mac_address_range')->nullable()->comment('mac范围录入');
                }
                if (!Schema::hasColumn('assemble_order_info', 'sn_range_origin_type')) {
                    $table->tinyInteger('sn_range_origin_type')->default(1)->comment('类型,{1:天启2:客户}');
                }
                if (!Schema::hasColumn('assemble_order_info', 'sn_no_range')) {
                    $table->json('sn_no_range')->nullable()->comment('sn范围录入');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_info', function (Blueprint $table) {
            //
        });
    }
}
