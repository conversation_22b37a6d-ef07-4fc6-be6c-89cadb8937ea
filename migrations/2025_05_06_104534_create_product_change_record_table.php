<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductChangeRecordTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('product_change_record')) {
            Schema::create('product_change_record', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->string('title',500)->comment('标题');
                $table->json('notice_type')->nullable()->comment('通知类型');
                $table->json('change_reason')->nullable()->comment('变更原因');
                $table->json('change_impact')->nullable()->comment('变更影响');
                $table->tinyInteger('privacy_level')->default(1)->comment('保密等级');
                $table->tinyInteger('importance_level')->default(1)->comment('重要级别');
                $table->json('notice_members')->nullable()->comment('通知成员');
                $table->text('notice_content')->nullable()->comment('通知内容');
                $table->text('notice_content_html')->nullable()->comment('通知内容html格式');
                $table->json('attachments')->nullable()->comment('附件');
                $table->tinyInteger('status')->default(1)->comment('状态');
                $table->integer('project_progress_id')->default(0)->comment('历史数据来源id');

                $table->unsignedInteger('created_by')->default(0)->comment('创建人');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_change_record', function (Blueprint $table) {
            //
        });
    }
}
