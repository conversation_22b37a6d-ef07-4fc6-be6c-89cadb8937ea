<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ModifyAssembleOrderAttachmentTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('assemble_order_attachment')) {
            Schema::table('assemble_order_attachment', function (Blueprint $table) {
                if (!Schema::hasColumn('production_order', 'assign_audit_user_id')) {
                    $table->integer('assign_audit_user_id')->default(0)->comment('指派审核人用户id');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_attachment', function (Blueprint $table) {
            //
        });
    }
}
