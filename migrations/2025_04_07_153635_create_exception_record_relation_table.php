<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateExceptionRecordRelationTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('exception_record_relation')) {
            Schema::create('exception_record_relation', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->integer('exception_record_id')->default(0)->comment('异常记录id');
                $table->tinyInteger('relate_type')->default(0)->comment('异常类型：{1:生产订单异常,2:组装订单异常...}');
                $table->string('relate_id')->default('')->comment('异常类型对应的表id');
                $table->string('relate_product_name')->default('')->comment('关联的产品名称');
                $table->string('relate_product_code')->default('')->comment('关联产品编码');
                $table->tinyInteger('is_first_related')->default(0)->comment('是否在该订单创建的异常');
                $table->integer('created_by')->default(0)->comment('创建人');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                $table->comment('异常记录关联表');
                $table->index(['relate_type', 'relate_id'], 'idx_type_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exception_record_relation', function (Blueprint $table) {
            //
        });
    }
}
