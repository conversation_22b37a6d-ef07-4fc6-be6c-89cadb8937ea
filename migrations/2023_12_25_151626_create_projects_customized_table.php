<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProjectsCustomizedTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('projects_customized')) {
            Schema::create('projects_customized', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('project_id')->comment('项目ID');
                $table->string('client_name', 128)->nullable()->comment('客户名称');
                $table->integer('sale_client_id')->default(0)->comment('销售系统客户ID');
                $table->integer('relation_sale_product_id')->default(0)->comment('关联销售系统中产品ID');
                $table->tinyInteger('urgency')->default(0)->comment('紧急程度');
                $table->integer('yewu_handler_id')->default(0)->comment('业务跟进员');
                $table->bigInteger('shipment_predicted')->default(0)->comment('出货预测');
                $table->bigInteger('amount')->default(0)->comment('产量数量');
                $table->char('curr', 8)->default('人民币')->comment('币种');
                $table->decimal('cost')->default(0)->comment('费用(万)');
                $table->string('project_industry', 128)->nullable()->comment('项目行业');
                $table->string('company_website', 256)->nullable()->comment('公司网址');
                $table->text('company_background')->nullable()->comment('公司背景');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_customized');
    }
}
