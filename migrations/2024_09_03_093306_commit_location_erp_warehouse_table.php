<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitLocationErpWarehouseTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        if (Schema::hasTable('erp_warehouse')) {
            Schema::table('erp_warehouse', function (Blueprint $table) {
                // 添加 location 列
                if (!Schema::hasColumn('erp_warehouse', 'location')) {
                    $table->string('location')->nullable()->comment('归属地')->after('status');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
