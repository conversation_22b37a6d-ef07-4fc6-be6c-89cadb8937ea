<?php

use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Schema\Schema;

class CreateExceptionRecordTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('exception_record')) {
            Schema::create('exception_record', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->tinyInteger('type')->default(0)->comment('异常类型：{1:生产订单异常,2:组装订单异常}');
                $table->string('product_main_code')->default('')->comment('产品的主编号，为产品编号去掉后6位规格标识');
                $table->string('product_code')->default('')->comment('产品编码');
                $table->string('product_name')->default('')->comment('产品名称');
                $table->string('title')->default('')->comment('标题');
                $table->text('description')->nullable()->comment('描述');
                $table->json('attachment_ids')->nullable()->comment('附件ID');
                $table->tinyInteger('status')->default(1)->comment('状态,{1:待处理,2:处理中,3:已处理,4:挂起}');
                $table->integer('created_by')->default(0)->comment('创建人');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                $table->comment('异常记录表');
                $table->index([
                    'type',
                    'product_main_code'
                ], 'idx_type_main_code');
                $table->index([
                    'type',
                    'product_code'
                ], 'idx_type_code');
                $table->index([
                    'type',
                    'title'
                ], 'idx_type_title');
                $table->index([
                    'type',
                    'product_name'
                ], 'idx_type_name');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exception_record', function (Blueprint $table) {
            //
        });
    }
}
