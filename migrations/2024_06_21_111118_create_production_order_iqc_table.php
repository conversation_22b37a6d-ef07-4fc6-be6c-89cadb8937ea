<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductionOrderIqcTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('production_order_iqc')) {
            Schema::create('production_order_iqc', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('production_order_id')->default(0)->comment('生产订单id');
                $table->integer('oa_qc_id')->default(0)->comment('oa_qc表id');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index('production_order_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_order_iqc');
    }
}
