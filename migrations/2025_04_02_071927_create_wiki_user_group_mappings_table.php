<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateWikiUserGroupMappingsTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (!Schema::hasTable('wiki_user_group_mappings')) {
                Schema::create('wiki_user_group_mappings', function (Blueprint $table) {
                    // 主键
                    $table->bigIncrements('mapping_id')->comment('关联ID');

                    // 字段
                    $table->bigInteger('user_id')->comment('用户ID');
                    $table->bigInteger('group_id')->comment('组ID');
                    $table->dateTime('deleted_at')->nullable()->comment('软删除时间');
                    $table->dateTime('created_at')->comment('创建时间');
                    $table->dateTime('updated_at')->comment('更新时间');

                    // 索引
                    $table->index('user_id');
                    $table->index('group_id');
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('wiki_user_group_mappings');
        }
    }