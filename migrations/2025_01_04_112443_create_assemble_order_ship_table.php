<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateAssembleOrderShipTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('assemble_order_ship')) {
            Schema::create('assemble_order_ship', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->integer('assemble_order_id')->unsigned()->default(0)->comment('组装订单id');
                $table->string('logistics_no')->default('')->comment('物流单号');
                $table->tinyInteger('shipping_type')->default(0)->comment('发货方式');
                $table->string('license_plate_number')->default('')->comment('车牌号');
                $table->integer('pieces_num')->default(0)->comment('件数');
                $table->integer('num')->default(0)->comment('数量');
                $table->dateTime('ship_time')->nullable()->comment('发货时间');
                $table->text('sn_number')->nullable()->comment('SN号');
                $table->json('images')->nullable()->comment('附件');
                $table->integer('created_by')->unsigned()->default(0)->comment('创建人');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                // 索引
                $table->index('assemble_order_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_ship', function (Blueprint $table) {
            //
        });
    }
}
