<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateMarketingPromotionReportTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('marketing_promotion_report')) {
            Schema::create('marketing_promotion_report', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('brand_name', 32);
                $table->char('brand_code', 16)->comment('品牌的code');
                $table->integer('platform_id')->comment('平台ID');
                $table->date('date')->comment('月份(每月1号)');
                $table->integer('users_count')->nullable()->comment('用户数（平台推广明细）');
                $table->integer('interact_count')->nullable()->comment('互动数（平台推广明细）');
                $table->integer('exposure_count')->nullable()->comment('暴光度（平台推广明细）');
                $table->decimal('click_rate', 6,2)->nullable()->comment('点击率%（平台推广明细）');
                $table->decimal('click_conversion_rate', 6,2)->nullable()->comment('点击转发率%（平台推广明细）');
                $table->decimal('invest_return_rate', 6,2)->nullable()->comment('投资回报率%（平台推广明细）');
                $table->decimal('expect_amount', 10)->nullable()->comment('预计使用金额(计划)');
                $table->decimal('practical_amount', 10)->nullable()->comment('实际使用金额(计划)');
                $table->integer('expect_display')->nullable()->comment('预计展现量(计划)');
                $table->integer('practical_display')->nullable()->comment('实际展现量(计划)');
                $table->integer('expect_click')->nullable()->comment('预计点击量(计划)');
                $table->integer('practical_click')->nullable()->comment('实际点击量(计划)');
                $table->decimal('expect_ctr', 6,2)->nullable()->comment('预计点击率(计划)');
                $table->decimal('practical_ctr', 6,2)->nullable()->comment('实际点击率(计划)');
                $table->decimal('expect_relay', 6,2)->nullable()->comment('预计转发率(计划)');
                $table->decimal('practical_relay', 6,2)->nullable()->comment('实际转发率(计划)');
                $table->decimal('expect_invest_return', 6,2)->nullable()->comment('预计投资回报率(计划)');
                $table->decimal('practical_invest_return', 6,2)->nullable()->comment('实际投资回报率(计划)');
                $table->integer('expect_users')->nullable()->comment('预计用户量(计划)');
                $table->integer('practical_users')->nullable()->comment('实际用户量(计划)');
                $table->integer('expect_interact')->nullable()->comment('预计互动数(计划)');
                $table->integer('practical_interact')->nullable()->comment('实际互动数(计划)');
                $table->integer('expect_exposure')->nullable()->comment('预计曝光度(计划)');
                $table->integer('practical_exposure')->nullable()->comment('实际曝光度(计划)');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketing_promotion_report');
    }
}
