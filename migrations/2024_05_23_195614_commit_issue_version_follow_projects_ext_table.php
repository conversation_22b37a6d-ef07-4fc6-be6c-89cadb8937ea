<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitIssueVersionFollowProjectsExtTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('projects_ext')) {
            Schema::table('projects_ext', function (Blueprint $table) {
                if (!Schema::hasColumn('projects_ext', 'issue_version_follow')) {
                    $table->tinyInteger('issue_version_follow')->default(1)->comment('事项版本是否跟随');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
