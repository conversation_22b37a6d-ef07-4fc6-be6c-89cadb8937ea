<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitReturnNumOaProductBorrowDetails extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oa_product_borrow_details', function (Blueprint $table) {
            // 添加已归还数量
            if (!Schema::hasColumn('oa_product_borrow_details', 'return_count')) {
                $table->smallInteger('return_count')->default(0)->comment('已归还数量');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
