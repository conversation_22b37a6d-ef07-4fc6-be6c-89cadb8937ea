<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitStarsBrandPromotionReportTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('marketing_promotion_report')) {
            Schema::table('marketing_promotion_report', function (Blueprint $table) {
                if (!Schema::hasColumn('marketing_promotion_report', 'stars')) {
                    $table->integer('stars')->nullable()->comment('关注数')->after('interact_count');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
