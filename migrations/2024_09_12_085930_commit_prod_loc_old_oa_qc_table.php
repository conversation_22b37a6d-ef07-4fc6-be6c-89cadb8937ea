<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitProdLocOldOaQcTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('oa_qc')) {
            Schema::table('oa_qc', function (Blueprint $table) {
                // 添加 prod_loc_old 列
                if (!Schema::hasColumn('oa_qc', 'prod_loc_old')) {
                    $table->string('prod_loc_old', 8)->nullable()
                        ->comment('旧的仓库编码（或未调拨前的）')->after('prod_loc');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
