<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitAtWorkCompanyIdOaUserFilesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oa_user_files', function (Blueprint $table) {
            if (! Schema::hasColumn('oa_user_files', 'at_work_company_id')) {
                $table->integer('at_work_company_id')->nullable()->comment('当前工作所在公司id');
            }
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
