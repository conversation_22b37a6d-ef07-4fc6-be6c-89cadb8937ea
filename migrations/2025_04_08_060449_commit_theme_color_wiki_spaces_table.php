<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitThemeColorWikiSpacesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wiki_spaces', function (Blueprint $table) {
            if (Schema::hasTable('wiki_spaces')) {
                Schema::table('wiki_spaces', function (Blueprint $table) {
                    if (!Schema::hasColumn('wiki_spaces', 'theme_color')) {
                        $table->string('theme_color', 20)->default('#3977F3')->comment('主题颜色')->after('description');
                    }
                });
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
