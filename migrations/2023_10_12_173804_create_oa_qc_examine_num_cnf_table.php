<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOaQcExamineNumCnfTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('oa_qc_examine_num_cnf')) {
            Schema::create('oa_qc_examine_num_cnf', function (Blueprint $table) {
                $table->increments('id');
                $table->integer('min_num')->default(0)->comment('最少数');
                $table->integer('max_num')->default(0)->comment('最大数');
                $table->integer('examine_num')->default(0)->comment('申检数');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('oa_qc_examine_num_cnf');
    }
}
