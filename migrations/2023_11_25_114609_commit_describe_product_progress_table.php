<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitDescribeProductProgressTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('product_progress')) {
            Schema::table('product_progress', function (Blueprint $table) {
                if (! Schema::hasColumn('product_progress', 'describe')) {
                    $table->text('describe')->nullable()->comment('描述');
                }
            });

        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_progress', function (Blueprint $table) {
            //
        });
    }
}
