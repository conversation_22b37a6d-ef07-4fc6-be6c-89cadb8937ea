<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitVersionPreProductProgress extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_progress', function (Blueprint $table) {
            if (!Schema::hasColumn('product_progress', 'version_pre')) {
                $table->string('version_pre', 20)->nullable()->comment('前版本号');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_progress', function (Blueprint $table) {
            //
        });
    }
}
