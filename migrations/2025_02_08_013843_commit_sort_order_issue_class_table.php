<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitSortOrderIssueClassTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('issue_class', function (Blueprint $table) {
            if (Schema::hasTable('issue_class')) {
                Schema::table('issue_class', function (Blueprint $table) {
                    if (!Schema::hasColumn('issue_class', 'sort_order')) {
                        $table->integer('sort_order')->default(0)->comment('排序顺序')->after('description');
                    }
                });
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
