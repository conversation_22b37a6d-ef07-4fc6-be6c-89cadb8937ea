<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitCreatedByUpdatedByTestCaseLibraryTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('test_case_library')) {
            Schema::table('test_case_library', function (Blueprint $table) {
                if (!Schema::hasColumn('test_case_library', 'created_by')) {
                    $table->integer('created_by')->nullable()->comment('创建人')->after('sort');
                }
                if (!Schema::hasColumn('test_case_library', 'updated_by')) {
                    $table->integer('updated_by')->nullable()->comment('更新人')->after('sort');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
