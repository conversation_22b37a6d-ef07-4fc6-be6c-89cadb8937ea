<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddNewFieldsToOaUserFiles extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('oa_user_files')) {
            Schema::table('oa_user_files', function (Blueprint $table) {
                // 添加摘要字段
                if (!Schema::hasColumn('oa_user_files', 'summary')) {
                    $table->text('summary')->nullable()->comment('摘要');
                }
                
                //查看次数 主要用于天启家园
                if (!Schema::hasColumn('oa_user_files', 'views')) {
                    $table->integer('views')->default(0)->comment('查看次数');
                }

                //操作人
                if (!Schema::hasColumn('oa_user_files', 'operator')) {
                    $table->integer('operator')->default(103)->comment('操作人');
                }
                
                // 银行卡
                if (!Schema::hasColumn('oa_user_files', 'bank_card')) {
                    $table->string('bank_card', 19)->nullable()->comment('银行卡');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('oa_user_files')) {
            Schema::table('oa_user_files', function (Blueprint $table) {
                // 回滚时删除添加的字段
                $columns = ['summary', 'views', 'operator', 'bank_card'];
                
                foreach ($columns as $column) {
                    if (Schema::hasColumn('oa_user_files', $column)) {
                        $table->dropColumn($column);
                    }
                }
            });
        }
    }
}