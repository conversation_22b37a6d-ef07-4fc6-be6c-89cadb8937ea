<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitPathToTestDirectoryTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('test_directory')) {
            Schema::table('test_directory', function (Blueprint $table) {
                if (!Schema::hasColumn('test_directory', 'path')) {
                    $table->string('path')->default('')->comment('由parent_id拼接成的分类路径')->after('parent_id');
                }

            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('test_directory', function (Blueprint $table) {
            //
        });
    }
}
