<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitExportFieldWikiDocumentStatisticsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wiki_document_statistics', function (Blueprint $table) {
            $table->date('date')->default(\Carbon\Carbon::now()->toDateString())->change()->comment('统计日期');
            $table->integer('export_count')->default(0)->after('comment_count')->comment('当日导出量');
            $table->datetime('last_export_at')->nullable()->after('export_count')->comment('最后导出时间');
            $table->integer('last_export_by')->nullable()->after('last_export_at')->comment('最后导出用户ID');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wiki_document_statistics', function (Blueprint $table) {
            $table->dropColumn(['export_count', 'last_export_at', 'last_export_by']);
            
            $table->date('date')->change()->comment('统计日期');
        });
    }
}
