<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddDefaultDescriptionToIssueTemplates extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('issue_templates', function (Blueprint $table) {
            if (!Schema::hasColumn('issue_templates', 'default_description')) {
                $table->text('default_description')
                    ->nullable()
                    ->after('description')
                    ->comment('事项默认描述内容');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('issue_templates', function (Blueprint $table) {
            if (Schema::hasColumn('issue_templates', 'default_description')) {
                $table->dropColumn('default_description');
            }
        });
    }
}