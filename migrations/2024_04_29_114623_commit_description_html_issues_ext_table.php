<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitDescriptionHtmlIssuesExtTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('issues_ext')) {
            Schema::table('issues_ext', function (Blueprint $table) {
                if (!Schema::hasColumn('issues_ext', 'description_html')) {
                    $table->longText('description_html')->nullable()->comment('内容html');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
