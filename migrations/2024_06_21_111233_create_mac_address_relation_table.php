<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateMacAddressRelationTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('mac_address_relation')) {
            Schema::create('mac_address_relation', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('mac_address_id')->default(0)->comment('mac地址id');
                $table->tinyInteger('relation_type')->default(0)->comment('绑定类型{1:生产订单}');
                $table->integer('relation_id')->default(0)->comment('绑定类型对应表id');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mac_address_relation');
    }
}
