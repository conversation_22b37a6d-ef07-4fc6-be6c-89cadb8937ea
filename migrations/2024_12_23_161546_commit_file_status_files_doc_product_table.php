<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitFileStatusFilesDocProductTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('files_doc_product')) {
            Schema::table('files_doc_product', function (Blueprint $table) {
                // 原字段添加默认值
                if (Schema::hasColumn('files_doc_product', 'file_status')) {
                    \Hyperf\DbConnection\Db::connection($this->connection)->update('UPDATE files_doc_product set file_status = 1 where file_status is null');
                    $table->string('file_status')->default(1)->change();
                }
                // 新建临时字段
                if (!Schema::hasColumn('files_doc_product', 'file_status_b')) {
                    $table->tinyInteger('file_status_b')->default(1)->comment('文件状态')->after('file_status');
                }
            });

            // 把数据复制到新的临时字段
            \Hyperf\DbConnection\Db::connection($this->connection)->update('UPDATE files_doc_product set file_status_b = file_status');

            Schema::table('files_doc_product', function (Blueprint $table) {
                // 删除旧字段
                if (Schema::hasColumn('files_doc_product', 'file_status')) {
                    $table->dropColumn('file_status');
                }
                // 重命名临时字段为正式字段
                if (Schema::hasColumn('files_doc_product', 'file_status_b')) {
                    $table->renameColumn('file_status_b', 'file_status')->change();
                }

            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
