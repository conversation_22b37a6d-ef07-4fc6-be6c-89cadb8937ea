<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ChangeProductIdProductDescValueTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // if (Schema::hasTable('product_desc_value')) {
        //     Schema::table('product_desc_value', function (Blueprint $table) {
        //         $table->primary(['product_id', 'desc_id']);
        //     });
        // }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
