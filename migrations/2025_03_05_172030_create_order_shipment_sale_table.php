<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOrderShipmentSaleTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('shipment_sale')) {
            Schema::create('shipment_sale', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->integer('shipment_id')->default(0)->comment('出库单id');
                $table->string('sale_id')->default('')->comment('销售系统销售单id');
                $table->tinyInteger('platform_id')->default(0)->comment('销售系统平台id');
                $table->integer('product_id')->default(0)->comment('销售系统产品id');
                $table->string('product_name')->default('')->comment('产品名称');
                $table->string('product_code')->default('')->comment('产品料号');
                $table->integer('product_num')->default(0)->comment('产品数量');
                $table->string('logistics_no')->default('')->comment('物流单号');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index('sale_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipment_sale', function (Blueprint $table) {
            //
        });
    }
}
