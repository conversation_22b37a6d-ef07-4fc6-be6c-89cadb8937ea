<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitFlowTemplateDefaultProductTypeTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('', function (Blueprint $table) {
            Schema::table('flow_template', function (Blueprint $table) {
                if (!Schema::hasColumn('flow_template', 'default_product_type')) {
                    $table->integer('default_product_type')->after('is_default')->nullable()->comment('流程图默认对应的产品类型');
                }
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
