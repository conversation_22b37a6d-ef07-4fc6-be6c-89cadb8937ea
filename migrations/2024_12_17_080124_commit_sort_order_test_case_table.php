<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitSortOrderTestCaseTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('test_case')) {
            Schema::table('test_case', function (Blueprint $table) {
                if (!Schema::hasColumn('test_case', 'sort_order')) {
                    $table->integer('sort_order')->default(0)->comment('排序顺序')->after('estimated_hours');
                }
            });
        }
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
