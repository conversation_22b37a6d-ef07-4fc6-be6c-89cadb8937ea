<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductDescValueTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('product_desc_value')) {
            Schema::create('product_desc_value', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('product_id')->comment('产品ID');
                $table->smallInteger('desc_id')->comment('描述属性ID');
                $table->smallInteger('value')->comment('属性值');
                $table->string('url')->comment('地址');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_desc_value');
    }
}
