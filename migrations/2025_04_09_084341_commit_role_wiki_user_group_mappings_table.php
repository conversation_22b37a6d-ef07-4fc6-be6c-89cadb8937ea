<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitRoleWikiUserGroupMappingsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wiki_user_group_mappings', function (Blueprint $table) {
            if (Schema::hasTable('wiki_user_group_mappings')) {
                Schema::table('wiki_user_group_mappings', function (Blueprint $table) {
                    if (!Schema::hasColumn('wiki_user_group_mappings', 'role')) {
                        $table->string('role', 50)->nullable()->comment('成员角色: owner，editor，member, visitor')->after('group_id');
                    }
                });
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
