<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitOrderNoMacAddressTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('mac_address')) {
            Schema::table('mac_address', function (Blueprint $table) {
                if (!Schema::hasColumn('mac_address', 'used_no')) {
                    $table->string('used_no', 32)->nullable(true)->comment('订单号');
                }
                if (!Schema::hasColumn('mac_address', 'used_type')) {
                    $table->smallInteger('used_type')->comment('订单类型1:(外委)生产订单');
                }
                if (!Schema::hasColumn('mac_address', 'used_user_id')) {
                    $table->integer('used_user_id')->default(0)->comment('使用人ID');
                }
                if (!Schema::hasColumn('mac_address', 'used_product')) {
                    $table->text('used_product')->nullable(true)->comment('使用的产品');
                }
                if (!Schema::hasColumn('mac_address', 'used_client')) {
                    $table->text('used_client')->nullable(true)->comment('使用的客户');
                }
                if (!Schema::hasColumn('mac_address', 'used_date')) {
                    $table->dateTime('used_date')->nullable(true)->comment('申请使用的日期');
                }
                if (!Schema::hasColumn('mac_address', 'decimal_base')) {
                    $table->bigInteger('decimal_base')->default(0)->comment('十进制数');
                }
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
