<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductionOrderInfoTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('production_order_info')) {
            Schema::create('production_order_info', function (Blueprint $table) {
                $table->bigIncrements('id');
                #创建人
                $table->integer('production_order_id')->unsigned()->default(0)->comment('production_order对应id');
                #硬件负责人
                $table->integer('hardware_user_id')->unsigned()->default(0)->comment('硬件负责人');
                #软件负责人
                $table->integer('software_user_id')->unsigned()->default(0)->comment('软件负责人');
                #结构负责人
                $table->integer('structure_user_id')->unsigned()->default(0)->comment('结构负责人');
                #订单负责人
                $table->integer('order_user_id')->unsigned()->default(0)->comment('订单负责人');
                #生产主管
                $table->integer('production_user_id')->unsigned()->default(0)->comment('生产主管');
                #测试主管
                $table->integer('test_user_id')->unsigned()->default(0)->comment('测试主管');
                #附件
                $table->json('attachments')->nullable()->comment('附件');
                $table->integer('attachments_upload_user_id')->default(0)->comment('附件上传人');
                #起始的mac地址
                $table->char('start_mac_address',20)->default('')->comment('起始的mac地址');
                #sn号
                $table->string('sn_start_no')->nullable()->default('')->comment('sn号');

                $table->tinyInteger('is_complete')->default(1)->comment('是否填写完整{0:否,1:是}');

                #预计上线
                $table->date('predict_online_time')->nullable()->default(null)->comment('预计上线时间');
                $table->date('actual_online_time')->nullable()->default(null)->comment('实际上线日期');
                #核准
                $table->tinyInteger('approve_status')->default(1)->comment('核准状态{1:待核准,2:已核准,3:挂起}');
                $table->date('approve_date')->nullable()->default(null)->comment('核准日期');
                #生产总结绑定
                $table->integer('pre_production_order_id')->default(0)->comment('上一次的生产订单id，用来展示上次的生产总结');

                $table->tinyInteger('base_file_upload_status')->default(0)->comment('基础文件上传状态{0:未完成,1:已完成}');
                $table->tinyInteger('base_file_audit_status')->default(0)->comment('基础文件审核状态{0:未审核,1:审核通过,2:审核不通过}');

                $table->tinyInteger('first_file_upload_status')->default(0)->comment('首件文件上传状态{0:未完成,1:已完成}');
                $table->tinyInteger('first_file_audit_status')->default(0)->comment('首件文件审核状态{0:未审核,1:审核通过,2:审核不通过}');

                $table->tinyInteger('summary_finish_status')->default(0)->comment('生产总结状态{0:新建,1:已解决,2:挂起}');

                #流程状态
                $table->integer('work_status_id')->default(0)->comment('对应work_status表的状态id');
                #创建时间
                $table->dateTime('created_at')->nullable();
                #更新时间
                $table->dateTime('updated_at')->nullable();
                #删除时间
                $table->dateTime('deleted_at')->nullable();
                #建立索引
                $table->index('production_order_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_order_info');
    }
}
