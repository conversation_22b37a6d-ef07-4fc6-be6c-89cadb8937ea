<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitAssembleOrderInfoTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        if (Schema::hasTable('assemble_order_info')) {
            Schema::table('assemble_order_info', function (Blueprint $table) {
                if (!Schema::hasColumn('assemble_order_info', 'order_user_id')) {
                    $table->integer('order_user_id')->default(0)->comment('订单负责人')->after('software_user_id');
                }

                if (!Schema::hasColumn('assemble_order_info', 'test_user_id')) {
                    $table->integer('test_user_id')->default(0)->comment('测试人')->after('order_user_id');
                }

                if (Schema::hasColumn('assemble_order_info', 'delivery_date')) {
                    $table->dropColumn('delivery_date');
                }
                if (Schema::hasColumn('assemble_order_info', 'stock_order_code')) {
                    $table->dropColumn('stock_order_code');
                }
                if (!Schema::hasColumn('assemble_order_info', 'sn_no_text')) {
                    $table->text('sn_no_text')->nullable()->comment('sn码')->after('start_sn_no');
                }
                if (!Schema::hasColumn('assemble_order_info', 'sn_no_attachment_id')) {
                    $table->integer('sn_no_attachment_id')->default(0)->comment('sn码上传文件')->after('sn_no_text');
                }
                if (!Schema::hasColumn('assemble_order_info', 'mac_address_text')) {
                    $table->text('mac_address_text')->nullable()->comment('mac地址')->after('start_mac_address');
                }
                if (!Schema::hasColumn('assemble_order_info', 'mac_address_attachment_id')) {
                    $table->integer('mac_address_attachment_id')->default(0)->comment('mac地址上传文件')->after('start_mac_address');
                }
                if (!Schema::hasColumn('assemble_order_info', 'predict_online_date')) {
                    $table->date('predict_online_date')->nullable()->comment('预计上线时间')->after('is_complete');
                }
                if (!Schema::hasColumn('assemble_order_info', 'actual_online_date')) {
                    $table->date('actual_online_date')->nullable()->comment('实际上线时间')->after('is_complete');
                }
                if (Schema::hasColumn('assemble_order_info', 'online_date')) {
                    $table->dropColumn('online_date');
                }
                if (Schema::hasColumn('assemble_order_info', 'first_back_date')) {
                    $table->dropColumn('first_back_date');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_info', function (Blueprint $table) {
            //
        });
    }
}
