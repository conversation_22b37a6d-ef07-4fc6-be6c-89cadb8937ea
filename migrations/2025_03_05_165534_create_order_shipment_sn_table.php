<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOrderShipmentSnTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('shipment_sale_sn')) {
            Schema::create('shipment_sale_sn', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->integer('shipment_id')->default(0)->comment('出库单id');
                $table->integer('shipment_sale_id')->default(0)->comment('销售单id');
                $table->string('sn_code')->default('')->comment('sn码');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index('shipment_id');
                $table->index('shipment_sale_id');
                $table->index('sn_code');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipment_sale_sn', function (Blueprint $table) {
            //
        });
    }
}
