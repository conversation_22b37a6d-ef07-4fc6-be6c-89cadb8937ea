<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ChangeSizeProjectsInfoTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('projects_info')) {
            Schema::table('projects_info', function (Blueprint $table) {
                if (Schema::hasColumn('projects_info', 'size')) {
                    $table->string('size', 128)->change();
                }
                if (Schema::hasColumn('projects_info', 'links')) {
                    $table->string('platform', 16)->nullable(true)->change();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
