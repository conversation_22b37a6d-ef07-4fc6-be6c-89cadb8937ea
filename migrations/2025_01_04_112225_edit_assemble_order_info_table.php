<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class EditAssembleOrderInfoTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('assemble_order_info')) {
            Schema::table('assemble_order_info', function (Blueprint $table) {
                if (!Schema::hasColumn('assemble_order_info', 'product_finished_data_status')) {
                    $table->integer('product_finished_data_status')->default(1)->comment('生产成品数据状态{1:未完成,2:已上传，3：已审核}');
                }
                if (!Schema::hasColumn('assemble_order_info', 'product_soft_data_status')) {
                    $table->integer('product_soft_data_status')->default(1)->comment('生产软件数据状态{1:未上传,2:已上传，3:已完成}');
                }
                if (!Schema::hasColumn('assemble_order_info', 'ship_data_status')) {
                    $table->integer('ship_data_status')->default(1)->comment('发货数据完成状态{1:未完成,2:已完成}');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_info', function (Blueprint $table) {
            //
        });
    }
}
