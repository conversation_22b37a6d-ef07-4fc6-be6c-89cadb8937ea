<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOaReturnOrderExpressTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('oa_return_order_express')) {
            Schema::create('oa_return_order_express', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('barcode', '32')->comment('快递单号');
                $table->string('com')->nullable()->comment('快递公司编号');
                $table->string('com_text')->nullable()->comment('快递公司名称');
                $table->string('image_url', '256')->nullable()->comment('快递单图片');
                $table->smallInteger('state')->nullable()->comment('快递状态');
                $table->tinyInteger('ischeck')->default(0)->comment('是否已签收');
                $table->text('receiver_text')->nullable()->comment('收件人内容');
                $table->decimal('receiver_conf')->nullable();
                $table->text('receiver_address')->nullable()->comment('收件人详细地址');
                $table->string('receiver_mobile', 16)->nullable()->comment('收件人手机号');
                $table->string('receiver_name', 16)->nullable()->comment('收件人姓名');
                $table->string('receiver_province', 16)->nullable()->comment('收件人省');
                $table->string('receiver_city', 16)->nullable()->comment('收件人市');
                $table->string('receiver_county', 16)->nullable()->comment('收件人区');
                $table->dateTime('receiver_time')->nullable()->comment('收件时间');
                $table->text('sender_text')->nullable()->comment('寄件人内容');
                $table->decimal('sender_conf')->nullable();
                $table->text('sender_address')->nullable()->comment('寄件人详细地址');
                $table->string('sender_mobile', 16)->nullable()->comment('寄件人手机号');
                $table->string('sender_name', 16)->nullable()->comment('寄件人姓名');
                $table->string('sender_province', 16)->nullable()->comment('寄件人省');
                $table->string('sender_city', 16)->nullable()->comment('寄件人市');
                $table->string('sender_county', 16)->nullable()->comment('寄件人区');
                $table->dateTime('sender_time')->nullable()->comment('寄件时间');
                $table->json('process')->nullable()->comment('快递过程');
                $table->integer('operator_id')->comment('操作员ID');
                $table->string('goods_name', '32')->nullable()->comment('物品名称');
                $table->integer('goods_num')->default(0)->comment('物品数量');
                $table->decimal('goods_weight')->default(0)->comment('物品重量G单位');
                $table->text('remark')->nullable()->comment('备注');
                $table->tinyInteger('receiver_compy')->default(0)->comment('接收公司1天启2萤火3思特森');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('oa_return_order_express');
    }
}

// {
//     "code": 200,
// 	"msg": "success",
// 	"time": 1709607690,
// 	"data": {
//     "code": 200,
// 		"message": "SUCCESS",
// 		"taskId": "e36e155beaa2416da8ff508a2e73a78a",
// 		"data": {
//         "barcode": [
//             "SF1681917087962"
//         ],
// 			"waybill": "SF1681917087962",
// 			"receiver": {
//             "text": "涨翔羽—183****1979江苏省南京市江宁区江宁开发区总部基地32栋17层南栖仙策",
// 				"conf": 0.9086,
// 				"address": "江宁开发区总部基地32栋17层南栖仙策",
// 				"mobile": "183****1979",
// 				"name": null,
// 				"province": "江苏省",
// 				"city": "南京市",
// 				"county": "江宁区"
// 			},
// 			"sender": {
//             "text": "陈杰鹤137****7706",
// 				"conf": 0.8125,
// 				"address": null,
// 				"mobile": "137****7706",
// 				"name": "陈杰鹤"
// 			}
// 		}
// 	}
// }
