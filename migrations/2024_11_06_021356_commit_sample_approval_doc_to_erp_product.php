<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitSampleApprovalDocToErpProduct extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('erp_product', function (Blueprint $table) {
            if (!Schema::hasColumn('erp_product', 'sample_approval_doc')) {
                $table->json('sample_approval_doc')->nullable()->comment('附件-样品承认书')->after('image');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('erp_product', function (Blueprint $table) {
            //
        });
    }
}
