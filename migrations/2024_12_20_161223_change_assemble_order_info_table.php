<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ChangeAssembleOrderInfoTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('assemble_order_info')) {
            Schema::table('assemble_order_info', function (Blueprint $table) {
                if (!Schema::hasColumn('assemble_order_info', 'completeness_status')) {
                    $table->integer('completeness_status')->default(1)->comment('齐料状态{1:等待中,2:备料中,3:已备未发,4:已转组装}');
                }
                if (!Schema::hasColumn('assemble_order_info', 'assemble_data_status')) {
                    $table->integer('assemble_data_status')->default(1)->comment('组装数据完成状态{1:未完成,2:已完成}');
                }
                if (!Schema::hasColumn('assemble_order_info', 'soft_data_status')) {
                    $table->integer('soft_data_status')->default(1)->comment('软件数据完成状态{1:未上传,2:已上传，3:已完成}');
                }
                if (!Schema::hasColumn('assemble_order_info', 'first_assemble_data_status')) {
                    $table->integer('first_assemble_data_status')->default(1)->comment('首件组装附件完成状态{1:未上传,2:已上传，3:已完成}');
                }
                if (!Schema::hasColumn('assemble_order_info', 'first_soft_data_status')) {
                    $table->integer('first_soft_data_status')->default(1)->comment('首件软件附件完成状态{1:未上传,2:已上传，3:已完成}');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_info', function (Blueprint $table) {
            //
        });
    }
}
