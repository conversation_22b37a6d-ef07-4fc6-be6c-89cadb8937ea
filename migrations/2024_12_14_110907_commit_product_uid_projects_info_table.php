<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitProductUidProjectsInfoTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('projects_info')) {
            Schema::table('projects_info', function (Blueprint $table) {
                if (!Schema::hasColumn('projects_info', 'product_manager_uid')) {
                    $table->integer('product_manager_uid')->default(0)->comment('产品负责人')->after('product_progress');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
