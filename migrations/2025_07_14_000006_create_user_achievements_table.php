<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateUserAchievementsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_achievements', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->unsignedBigInteger('achievement_id')->comment('成就ID');
            $table->string('achievement_code', 50)->comment('成就代码(冗余字段，便于查询)');
            $table->integer('progress')->default(0)->comment('当前进度值');
            $table->integer('target_value')->comment('目标值');
            $table->decimal('progress_percentage', 5, 2)->default(0.00)->comment('完成百分比');
            $table->tinyInteger('is_completed')->default(0)->comment('是否已完成');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->timestamp('notified_at')->nullable()->comment('通知时间');
            $table->tinyInteger('is_displayed')->default(1)->comment('是否在用户页面显示');
            $table->integer('completion_count')->default(0)->comment('完成次数(可重复成就)');
            $table->timestamp('last_updated_at')->nullable()->comment('进度最后更新时间');
            $table->json('metadata')->nullable()->comment('扩展数据');
            $table->timestamps();

            $table->unique(['user_id', 'achievement_id'], 'uk_user_achievement');
            $table->index('user_id', 'idx_user_id');
            $table->index('achievement_id', 'idx_achievement_id');
            $table->index('is_completed', 'idx_is_completed');
            $table->index('completed_at', 'idx_completed_at');
            $table->index('progress_percentage', 'idx_progress_percentage');
        });

        \Hyperf\DbConnection\Db::statement("ALTER TABLE `bi_user_achievements` comment '积分系统-用户成就记录表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_achievements');
    }
}