<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Schema\Schema;

class CreateAssembleOrderSummaryTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (! Schema::hasTable('assemble_order_summary')) {
            Schema::create('assemble_order_summary', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->integer('assemble_order_id')->unsigned()->default(0)->comment('组装订单id');
                $table->float('assemble_capacity', 15)->default(0)->comment('组装产能');
                $table->float('test_capacity', 15)->default(0)->comment('测试产能');
                $table->decimal('assemble_excellent_rate', 5)->default(0.00)->comment('组装良率');
                $table->decimal('test_excellent_rate', 5)->default(0.00)->comment('测试良率');
                $table->text('assemble_remark')->nullable()->comment('组装问题备注');
                $table->text('assemble_other_remark')->nullable()->comment('组装其他备注');
                $table->text('test_remark')->nullable()->comment('测试问题备注');
                $table->text('test_other_remark')->nullable()->comment('测试其他备注');
                $table->json('assemble_attachment_ids')->nullable()->comment('组装附件');
                $table->json('test_attachment_ids')->nullable()->comment('测试附件');
                $table->tinyInteger('assemble_status')->default(1)->comment('组装状态{1:未完成,2:已完成}');
                $table->tinyInteger('test_status')->default(1)->comment('测试状态{1:未完成,2:已完成}');
                $table->tinyInteger('status')->default(1)->comment('总结状态{1:新建,2:已解决,3:挂起}');
                $table->integer('assemble_user_id')->default(0)->comment('组装人员');
                $table->integer('test_user_id')->default(0)->comment('测试人员');
                $table->dateTime('assemble_time')->nullable()->comment('组装时间');
                $table->dateTime('test_time')->nullable()->comment('测试时间');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                // 索引
                $table->index('assemble_order_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_summary', function (Blueprint $table) {
        });
    }
}
