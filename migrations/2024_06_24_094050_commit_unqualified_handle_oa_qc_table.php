<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitUnqualifiedHandleOaQcTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oa_qc', function (Blueprint $table) {
            if (!Schema::hasColumn('oa_qc', 'unqualified_handle')) {
                $table->tinyInteger('unqualified_handle')->after('handle')->nullable()->default(0)->comment('不合格处理方案');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
