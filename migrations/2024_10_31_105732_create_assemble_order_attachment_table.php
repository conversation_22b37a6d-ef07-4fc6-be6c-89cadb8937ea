<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateAssembleOrderAttachmentTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assemble_order_attachment', function (Blueprint $table) {
            //
        });
        if (!Schema::hasTable('assemble_order_attachment')) {
            Schema::create('assemble_order_attachment', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('assemble_order_id')->default(0)->comment('组装订单id');
                $table->integer('attachment_id')->default(0)->comment('附件id');
                $table->string('attachment_remark')->default('')->comment('附件链接或相关地址');
                $table->integer('type')->default(0)->comment('文件类型');
                $table->integer('upload_user_id')->default(0)->comment('上传用户');
                $table->integer('audit_user_id')->default(0)->comment('审核用户');
                $table->tinyInteger('audit_status')->default(1)->comment('审核状态{1:待审核,2:已审核,3:未通过}');
                $table->dateTime('upload_time')->nullable()->comment('上传时间');
                $table->dateTime('audit_time')->nullable()->comment('审核时间');
                $table->string('reject_reason')->default('')->comment('驳回理由');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index('assemble_order_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_attachment', function (Blueprint $table) {
            //
        });
    }
}
