<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddIndexesToTestCaseTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('test_case', function (Blueprint $table) {
            $table->index('library_id', 'idx_library_id');      // 添加 library_id 索引
            $table->index('directory_id', 'idx_directory_id');  // 添加 directory_id 索引
            $table->index('project_id', 'idx_project_id');      // 添加 project_id 索引
            $table->index('priority', 'idx_priority');          // 添加 priority 索引
            $table->index('category_id', 'idx_category_id');    // 添加 category_id 索引
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('test_case', function (Blueprint $table) {
            $table->dropIndex('idx_library_id');      // 删除 library_id 索引
            $table->dropIndex('idx_directory_id');    // 删除 directory_id 索引
            $table->dropIndex('idx_project_id');      // 删除 project_id 索引
            $table->dropIndex('idx_priority');        // 删除 priority 索引
            $table->dropIndex('idx_category_id');     // 删除 category_id 索引
        });
    }
}
