<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ModifyProductionOrderAttachmentTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if(Schema::hasTable('production_order_attachment')) {
            Schema::table('production_order_attachment', function (Blueprint $table) {
                if (!Schema::hasColumn('production_order_attachment', 'is_extra')) {
                    $table->tinyInteger('is_extra')->default(0)->comment('是否是额外录入的文件')->after('reject_reason');   ;
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_order_attachment', function (Blueprint $table) {
            //
        });
    }
}
