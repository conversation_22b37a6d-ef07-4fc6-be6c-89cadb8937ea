<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateUserNoticeSubscribeTable extends Migration
{
    /**
     * 用户消息订阅表
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('user_notice_subscribe')) {
            Schema::create('user_notice_subscribe', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('user_id')->comment('uid');
                $table->integer('notice_id')->default(0)->comment('订阅的消息ID');
                $table->string('notice_type', 32)->default('')->comment('访问的消息类型');
                $table->string('subscribe_type', 16)->comment('采用的订阅方法id|type');
                $table->json('subscribe_mode')->comment('订阅渠道');
                $table->tinyInteger('is_subscribe')->default(1)->comment('是否订阅');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_notice_subscribe');
    }
}
