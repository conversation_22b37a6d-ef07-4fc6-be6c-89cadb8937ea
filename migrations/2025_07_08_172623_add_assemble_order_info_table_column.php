<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddAssembleOrderInfoTableColumn extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('assemble_order_info')) {
            Schema::table('assemble_order_info', function (Blueprint $table) {
                if (!Schema::hasColumn('assemble_order_info', 'tag_status')) {
                    $table->tinyInteger('tag_audit_status')->default(1)->comment('便签审核状态{1:待审核,2:已审核,3:未通过}');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_info', function (Blueprint $table) {
            //
        });
    }
}
