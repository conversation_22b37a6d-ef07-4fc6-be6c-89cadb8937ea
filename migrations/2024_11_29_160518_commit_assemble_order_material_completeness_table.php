<?php

use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Schema\Schema;

class CommitAssembleOrderMaterialCompletenessTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('assemble_order_material_completeness')) {
            Schema::table('assemble_order_material_completeness', function (Blueprint $table) {
                if (!Schema::hasColumn('assemble_order_material_completeness', 'images')) {
                    $table->json('images')->nullable()->comment('图片')->after('status');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_material_completeness', function (Blueprint $table) {
            //
        });
    }
}
