<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitAttachmentReversionProductProgress extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_progress', function (Blueprint $table) {
            //增加附件字段
                if (!Schema::hasColumn('product_progress', 'attachment_reversion')) {
                    $table->json('attachment_reversion')
                        ->nullable()
                        ->comment('改版记录的附件');
                }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
