<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitSortOrderTestDirectoryTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('test_directory')) {
            Schema::table('test_directory', function (Blueprint $table) {
                if (!Schema::hasColumn('test_directory', 'sort_order')) {
                    $table->integer('sort_order')->default(0)->comment('排序顺序')->after('description');
                }
            });
        }
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
