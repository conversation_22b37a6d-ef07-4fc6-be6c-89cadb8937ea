<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitCatalogIdWikiDocumentTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('wiki_documents') && !Schema::hasColumn('wiki_documents', 'catalog_id')) {
            Schema::table('wiki_documents', function (Blueprint $table) {
                $table->unsignedBigInteger('catalog_id')->default(0)->comment('目录ID')->after('space_id');
                $table->index('catalog_id', 'wiki_documents_catalog_id_index'); // 添加索引并指定索引名
            });
        }
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
