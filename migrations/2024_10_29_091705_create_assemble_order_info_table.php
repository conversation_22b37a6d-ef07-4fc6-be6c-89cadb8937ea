<?php

use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Schema\Schema;

class CreateAssembleOrderInfoTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('assemble_order_info')) {
            Schema::create('assemble_order_info', function (Blueprint $table) {
                $table->bigIncrements('id')->comment('id');
                $table->integer('assemble_order_id')->default(0)->comment('组装订单id');
                $table->string('stock_order_code')->default('')->comment('备货单号');
                $table->date('online_date')->nullable()->comment('上线日期');
                $table->tinyInteger('order_type')->default(1)->comment('订单类型');
                $table->date('first_back_date')->nullable()->comment('首次回货日期');
                $table->tinyInteger('assemble_type')->default(1)->comment('组装类型');
                $table->date('delivery_date')->nullable()->comment('交货日期');
                $table->integer('assemble_user_id')->default(0)->comment('组装负责人');
                $table->integer('software_user_id')->default(0)->comment('软件负责人');
                $table->tinyInteger('shipment_place')->default(0)->comment('交货地点');
                $table->string('start_sn_no')->default('')->comment('初始SN号');
                $table->string('start_mac_address')->default('')->comment('MAC地址');
                $table->tinyInteger('is_complete')->default(0)->comment('是否填写完整(0:否, 1:是)');
                $table->json('attachment_ids')->nullable()->comment('附件');
                $table->tinyInteger('material_status')->default(1)->comment('齐料状态(1:否, 2:完成 ，3部分)');
                $table->text('material_remark')->nullable()->comment('材料情况');
                $table->text('other_remark')->nullable()->comment('其他备注');
                $table->tinyInteger('summary_finish_status')->default(1)->comment('汇总完成状态(1:未完成, 2:已完成)');
                $table->tinyInteger('approve_status')->default(1)->comment('核准状态(1:待核准, 2:已核准, 3:挂起)');
                $table->date('approve_date')->nullable()->comment('核准日期');
                $table->integer('work_status_id')->default(0)->comment('对应work_status表的状态id');
                $table->integer('pre_assemble_order_id')->default(0)->comment('上一次的组装订单id，用来展示上次的总结');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间');
                #建立索引
                $table->index('assemble_order_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order_info', function (Blueprint $table) {
            //
        });
    }
}
