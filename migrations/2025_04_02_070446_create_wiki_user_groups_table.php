<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateWikiUserGroupsTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (!Schema::hasTable('wiki_user_groups')) {
                Schema::create('wiki_user_groups', function (Blueprint $table) {
                    // 主键
                    $table->bigIncrements('group_id')->comment('组唯一ID');

                    // 字段
                    $table->string('group_name', 50)->comment('组名称');
                    $table->text('description')->nullable()->comment('组描述');
                    $table->bigInteger('created_by')->comment('创建人ID');
                    $table->dateTime('created_at')->comment('创建时间');
                    $table->dateTime('updated_at')->comment('更新时间');
                    $table->dateTime('deleted_at')->nullable()->comment('软删除时间');

                    // 索引
                    $table->index('group_name');
                    $table->index('created_by');
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('wiki_user_groups');
        }
    }