<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOrderMacSnBatchTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('order_mac_sn_batch')) {
            Schema::create('order_mac_sn_batch', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->tinyInteger('order_type')->default(0)->comment('订单类型');
                $table->integer('order_id')->default(0)->comment('订单id');
                $table->integer('batch_num')->default(0)->comment('批次号');
                $table->string('batch_key', 255)->default('')->comment('批次标识');
                $table->integer('mac_id')->default(0)->comment('macId');
                $table->integer('sn_id')->default(0)->comment('sn码id');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index(['order_type','order_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_mac_sn_batch', function (Blueprint $table) {
            //
        });
    }
}
