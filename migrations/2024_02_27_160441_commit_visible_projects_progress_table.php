<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitVisibleProjectsProgressTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('projects_progress')) {
            Schema::table('projects_progress', function (Blueprint $table) {
                $table->tinyInteger('visible')->default(1)->comment('跟进可见类型1公开2通知人可见3指定人可见4指定人不可见');
                $table->json('visible_user')->nullable()->comment('可见用户或不可见用户');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
