<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitIssueExtendWatcherProjectsExtTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('projects_ext')) {
            Schema::table('projects_ext', function (Blueprint $table) {
                if (!Schema::hasColumn('projects_ext', 'issue_extend_watcher')) {
                    $table->tinyInteger('issue_extend_watcher')->default(0)->comment('子事项是否父事项关注人');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
