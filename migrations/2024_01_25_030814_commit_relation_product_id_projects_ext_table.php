<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitRelationProductIdProjectsExtTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects_ext', function (Blueprint $table) {
            //
            if (!Schema::hasColumn('projects_ext', 'relation_product_id')) {
                $table->json('relation_product_id')->nullable()->comment('关联产品ID');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
