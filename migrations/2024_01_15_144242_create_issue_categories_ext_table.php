<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateIssueCategoriesExtTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('issue_categories_ext')) {
            Schema::create('issue_categories_ext', function (Blueprint $table) {
                $table->bigInteger('category_id')->primary();
                $table->smallInteger('position');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('issue_categories_ext');
    }
}
