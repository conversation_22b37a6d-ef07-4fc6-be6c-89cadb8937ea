<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ChangeProductionOrderTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('production_order', function (Blueprint $table) {
            //
        });
        if (Schema::hasTable('production_order')) {
            Schema::table('production_order', function (Blueprint $table) {
                if (!Schema::hasColumn('production_order', 'stock_order_code')) {
                    $table->string('stock_order_code')->default('')->comment('备货单号');
                }
                if (!Schema::hasColumn('production_order', 'stock_remark')) {
                    $table->text('stock_remark')->nullable()->comment('备货备注');
                }
                if (!Schema::hasColumn('production_order', 'stock_delivery_date')) {
                    $table->date('stock_delivery_date')->nullable()->comment('交货日期');
                }
                if (!Schema::hasColumn('production_order', 'stock_user')) {
                    $table->string('stock_user')->default('')->comment('业务员');
                }
                if (!Schema::hasColumn('production_order', 'stock_user_id')) {
                    $table->integer('stock_user_id')->default(0)->comment('业务员用户id');
                }


            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_order', function (Blueprint $table) {
            //
        });
    }
}
