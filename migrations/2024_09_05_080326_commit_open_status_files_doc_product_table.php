<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitOpenStatusFilesDocProductTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('files_doc_product')) {
        Schema::table('files_doc_product', function (Blueprint $table) {
            // 添加 open_status 列
            if (!Schema::hasColumn('files_doc_product', 'location')) {
                $table->tinyInteger('open_status')->default(1)->comment('是否启用')->after('file_status');
            }
        });
    }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
