<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateMarketingPlatformShowTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('marketing_platform_show')) {
            Schema::create('marketing_platform_show', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->tinyInteger('platform_id')->comment('平台ID');
                $table->smallInteger('show_type_id')->comment('显示类型 1:销售平台 2:推广平台 3:用户分析');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketing_platform_show');
    }
}
