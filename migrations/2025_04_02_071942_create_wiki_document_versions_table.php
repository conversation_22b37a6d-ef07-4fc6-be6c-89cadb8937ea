<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateWikiDocumentVersionsTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (!Schema::hasTable('wiki_document_versions')) {
                Schema::create('wiki_document_versions', function (Blueprint $table) {
                    // 主键
                    $table->bigIncrements('version_id')->comment('版本ID');

                    // 字段
                    $table->bigInteger('doc_id')->comment('文档ID');
                    $table->integer('version_number')->comment('版本号');
                    $table->longText('content_html')->nullable()->comment('HTML内容');
                    $table->longText('content_markdown')->nullable()->comment('Markdown内容');
                    $table->tinyInteger('editor_type')->comment('编辑器类型');
                    $table->bigInteger('created_by')->comment('创建人ID');
                    $table->dateTime('deleted_at')->nullable()->comment('软删除时间');
                    $table->dateTime('created_at')->comment('创建时间');
                    $table->dateTime('updated_at')->comment('更新时间');
                    $table->text('change_log')->nullable()->comment('变更说明');

                    // 索引
                    $table->index('doc_id');
                    $table->index('version_number');
                    $table->index('created_by');
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('wiki_document_versions');
        }
    }
