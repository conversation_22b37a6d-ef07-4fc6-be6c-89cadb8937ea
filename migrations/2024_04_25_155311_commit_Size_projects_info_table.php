<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitSizeProjectsInfoTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('projects_info')) {
            Schema::table('projects_info', function (Blueprint $table) {
                if (!Schema::hasColumn('projects_info', 'size')) {
                    $table->string('size', 32)->nullable()->comment('规格');
                }
                if (!Schema::hasColumn('projects_info', 'links')) {
                    $table->json('links')->nullable(true)->comment('链接');
                }
                if (!Schema::hasColumn('projects_info', 'delivery_inventory')) {
                    $table->text('delivery_inventory')->nullable()->comment('发货清单');
                }
                if (!Schema::hasColumn('projects_info', 'support')) {
                    $table->text('support')->nullable()->comment('支持列表');
                }
                if (!Schema::hasColumn('projects_info', 'size_book')) {
                    $table->text('size_book')->nullable()->comment('规格书');
                }
                if (!Schema::hasColumn('projects_info', 'online_status')) {
                    $table->tinyInteger('online_status')->default(0)->comment('上线状态');
                }
                if (!Schema::hasColumn('projects_info', 'remarks')) {
                    $table->longText('remarks')->nullable()->comment('备注');
                }
                if (!Schema::hasColumn('projects_info', 'accessories_category')) {
                    $table->smallInteger('accessories_category')->default(0)->comment('配件分类');
                }
                if (!Schema::hasColumn('projects_info', 'material')) {
                    $table->string('material', 32)->nullable()->comment('料号');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
