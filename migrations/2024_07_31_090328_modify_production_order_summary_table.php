<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ModifyProductionOrderSummaryTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if(Schema::hasTable('production_order_summary')) {
            Schema::table('production_order_summary', function (Blueprint $table) {
                if (!Schema::hasColumn('production_order_summary', 'chip_status')) {
                    $table->tinyInteger('chip_status')->default(0)->comment('贴片完成状态{0:未完成,1:已完成 }')->after('attachments');   ;
                }
                if (!Schema::hasColumn('production_order_summary', 'test_status')) {
                    $table->tinyInteger('test_status')->default(0)->comment('测试完成状态{0:未完成,1:已完成 }')->after('chip_status');   ;
                }
                if (!Schema::hasColumn('production_order_summary', 'production_other_remark')) {
                    $table->text('production_other_remark')->nullable()->comment('生产总结其他问题备注')->after('production_remark');
                }
                if (!Schema::hasColumn('production_order_summary', 'test_remark')) {
                    $table->text('test_remark')->nullable()->comment('测试备注')->after('production_other_remark');
                }
                if (!Schema::hasColumn('production_order_summary', 'test_other_remark')) {
                    $table->text('test_other_remark')->nullable()->comment('测试其他备注')->after('test_remark');
                }
                if (!Schema::hasColumn('production_order_summary', 'test_attachments')) {
                    $table->json('test_attachments')->nullable()->comment('测试附件');
                }
                if (Schema::hasColumn('production_order_summary', 'new_material')) {
                    $table->dropColumn('new_material');
                }
                if (Schema::hasColumn('production_order_summary', 'new_material')) {
                    $table->dropColumn('special_material');
                }

            });
        }
        Schema::table('production_order_summary', function (Blueprint $table) {
            //
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_order_summary', function (Blueprint $table) {
            //
        });
    }
}
