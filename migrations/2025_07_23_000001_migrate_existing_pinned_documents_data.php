<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;
use Hyperf\DbConnection\Db;
use Carbon\Carbon;

class MigrateExistingPinnedDocumentsData extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 处理现有的置顶文档数据
        $now = Carbon::now();
        $oneDayAgo = $now->copy()->subDay();
        
        // 为所有当前置顶的文档设置撤回截止时间（置顶时间+1天）
        Db::statement("
            UPDATE bi_wiki_documents
            SET pinned_revoke_deadline = DATE_ADD(pinned_at, INTERVAL 1 DAY)
            WHERE is_pinned = 1
            AND pinned_at IS NOT NULL
            AND pinned_revoke_deadline IS NULL
        ");
        
        // 将所有置顶时间超过1天的文档标记为曾被置顶
        Db::statement("
            UPDATE bi_wiki_documents
            SET ever_pinned = 1,
                ever_pinned_at = pinned_at
            WHERE is_pinned = 1
            AND pinned_at IS NOT NULL
            AND pinned_at <= ?
            AND ever_pinned = 0
        ", [$oneDayAgo->toDateTimeString()]);

        // 记录处理结果
        $processedCount = Db::table('wiki_documents')
            ->where('ever_pinned', 1)
            ->count();
            
        echo "数据迁移完成，共处理 {$processedCount} 个曾被置顶的文档\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 回滚操作：清空新添加的字段数据
        Db::statement("
            UPDATE bi_wiki_documents
            SET ever_pinned = 0,
                ever_pinned_at = NULL,
                pinned_revoke_deadline = NULL
        ");
        
        echo "数据回滚完成\n";
    }
}
