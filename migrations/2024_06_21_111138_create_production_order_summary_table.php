<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductionOrderSummaryTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('production_order_summary')) {
            Schema::create('production_order_summary', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('production_order_id')->unsigned()->default(0)->comment('生产订单id');
                $table->json('new_material')->nullable()->comment('新物料跟踪');
                $table->json('special_material')->nullable()->comment('特采物料跟踪');
                $table->float('chip_production_capacity',15)->default(0)->comment('贴片产能');
                $table->float('test_production_capacity',15)->default(0)->comment('测试产能');
                $table->decimal('patches_excellent_rate',5)->default(0.00)->comment('贴片良率');
                $table->decimal('test_excellent_rate',5)->default(0.00)->comment('测试良率');
                $table->text('production_remark')->nullable()->comment('生产问题备注');
                $table->text('other_remark')->nullable()->comment('其他备注');
                $table->json('attachments')->nullable()->comment('附件');
                $table->tinyInteger('status')->default(0)->comment('总结状态{0:新建,1:已解决,2:挂起}');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                //索引
                $table->index('production_order_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_order_summary');
    }
}
