<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreatePointOperationLogsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('point_operation_logs', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->enum('operation_type', ['add', 'subtract', 'reset', 'transfer'])->comment('操作类型');
            $table->integer('before_points')->comment('操作前积分');
            $table->integer('after_points')->comment('操作后积分');
            $table->integer('point_change')->comment('积分变化量');
            $table->string('operation_reason', 255)->comment('操作原因');
            $table->unsignedBigInteger('operator_id')->nullable()->comment('操作员ID(系统操作为NULL)');
            $table->enum('operator_type', ['system', 'admin', 'user'])->default('system')->comment('操作员类型');
            $table->unsignedBigInteger('related_record_id')->nullable()->comment('关联的积分记录ID');
            $table->string('batch_id', 50)->nullable()->comment('批量操作ID');
            $table->string('ip_address', 45)->nullable()->comment('操作IP');
            $table->string('user_agent', 500)->nullable()->comment('用户代理');
            $table->timestamps();

            $table->index('user_id', 'idx_user_id');
            $table->index('operation_type', 'idx_operation_type');
            $table->index('operator_id', 'idx_operator_id');
            $table->index('created_at', 'idx_created_at');
            $table->index('batch_id', 'idx_batch_id');
        });

        \Hyperf\DbConnection\Db::statement("ALTER TABLE `bi_point_operation_logs` comment '积分系统-积分操作日志表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('point_operation_logs');
    }
}