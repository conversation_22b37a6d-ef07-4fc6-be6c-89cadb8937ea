<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitNotesHtmlJournalExtTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('journal_ext')) {
            Schema::table('journal_ext', function (Blueprint $table) {
                if (!Schema::hasColumn('journal_ext', 'notes_html')) {
                    $table->longText('notes_html')->nullable()->comment('内容html');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
