<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitSnProductBorrowDetailsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oa_product_borrow_details', function (Blueprint $table) {
            if (!Schema::hasColumn('oa_product_borrow_details', 'borrow_sn')) {
                $table->string('borrow_sn', 32)->nullable()->comment('借用凭证号');
            }
            if (!Schema::hasColumn('oa_product_borrow_details', 'return_sn')) {
                $table->string('return_sn', 32)->nullable()->comment('归还凭证号');
            }
            if (!Schema::hasColumn('oa_product_borrow_details', 'is_apply_return')) {
                $table->unsignedTinyInteger('is_apply_return')->default(0)->comment('是否申请归还1:是,0:否');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('oa_product_borrow_details', function (Blueprint $table) {
            //
        });
    }
}
