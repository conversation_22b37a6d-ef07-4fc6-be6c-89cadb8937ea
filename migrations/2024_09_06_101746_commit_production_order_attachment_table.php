<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitProductionOrderAttachmentTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('production_order_attachment')) {
            Schema::table('production_order_attachment', function (Blueprint $table) {
                if (!Schema::hasColumn('production_order_attachment', 'link')) {
                    $table->string('link',500)->default('')->comment('超链接')->after('attachment_remark');
                }
                if (Schema::hasColumn('production_order_attachment', 'attachment_remark')) {
                    $table->string('attachment_remark')->comment('备注')->change();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_order_attachment', function (Blueprint $table) {
            //
        });
    }
}
