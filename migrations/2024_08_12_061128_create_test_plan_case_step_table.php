<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateTestPlanCaseStepTable extends Migration
    {
        /**
         * 指定数据库连接
         */
        protected $connection = 'tchip_redmine';

        /**
         * Run the migrations.
         */
        public function up(): void
        {
            Schema::create('test_plan_case_step', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('plan_case_result_id')->default(0)->comment('测试计划用例结果ID');
                $table->integer('test_plan_case_id')->default(0)->comment('测试计划用例ID');
                $table->integer('base_step_id')->default(0)->comment('基础用例步骤ID');
                $table->text('actual_result')->nullable()->comment('实际结果');
                $table->tinyInteger('result_status')->default(0)->comment('实际结果状态');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间');
                //索引
                $table->index('test_plan_case_id');
                $table->index('plan_case_result_id');

            });
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('test_plan_case_step');
        }
    }
