<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddEverPinnedFieldsToWikiDocumentsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wiki_documents', function (Blueprint $table) {
            // 添加曾被置顶相关字段
            $table->tinyInteger('ever_pinned')->default(0)->comment('是否曾经被置顶：0=从未置顶，1=曾经置顶')->after('pinned_reason');
            $table->timestamp('ever_pinned_at')->nullable()->comment('首次被置顶时间')->after('ever_pinned');
            $table->timestamp('pinned_revoke_deadline')->nullable()->comment('置顶撤回截止时间，用作记录曾真正置顶而非误操作的置顶')->after('ever_pinned_at');
            
            // 添加索引以提高查询性能
            $table->index(['ever_pinned'], 'idx_ever_pinned');
            $table->index(['ever_pinned_at'], 'idx_ever_pinned_at');
            $table->index(['pinned_revoke_deadline'], 'idx_pinned_revoke_deadline');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wiki_documents', function (Blueprint $table) {
            // 删除索引
            $table->dropIndex('idx_ever_pinned');
            $table->dropIndex('idx_ever_pinned_at');
            $table->dropIndex('idx_pinned_revoke_deadline');
            
            // 删除字段
            $table->dropColumn(['ever_pinned', 'ever_pinned_at', 'pinned_revoke_deadline']);
        });
    }
}
