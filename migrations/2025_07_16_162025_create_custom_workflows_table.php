<?php
declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateCustomWorkflowsTable extends Migration
{
    protected $connection = 'tchip_redmine';

    public function up(): void
    {

        Schema::create('custom_workflows', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->nullable(false)->comment('项目ID');
            $table->integer('old_status_id')->nullable(false)->comment('原状态ID');
            $table->integer('new_status_id')->nullable(false)->comment('新状态ID');
            $table->integer('role_id')->nullable(false)->comment('角色ID');
            $table->boolean('assignee')->default(false)->comment('指派人可操作');
            $table->boolean('author')->default(false)->comment('创建人可操作');
            $table->string('workflow_type', 30)->default('Issue')->comment('工作流类型');
            $table->string('field_name', 30)->nullable()->default(null)->comment('字段名');
            $table->text('rule')->nullable()->comment('规则');
            $table->integer('template_id')->nullable()->default(null)->comment('模板ID');
            $table->string('template_name', 100)->nullable()->default(null)->comment('模板名称');
            $table->timestamps();

            // 添加索引
            $table->index('project_id', 'idx_project');
            $table->index('old_status_id', 'idx_old_status');
            $table->index('new_status_id', 'idx_new_status');
            $table->index('role_id', 'idx_role');
            $table->index('template_id', 'idx_template');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('custom_workflows');
    }
}