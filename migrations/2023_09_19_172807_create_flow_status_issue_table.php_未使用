<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFlowStatusIssueTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('flow_status_issue')) {
            Schema::create('flow_status_issue', function (Blueprint $table) {
                $table->bigInteger('detail_id');
                $table->tinyInteger('status');
                $table->integer('issue_id');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flow_status_issue');
    }
}
