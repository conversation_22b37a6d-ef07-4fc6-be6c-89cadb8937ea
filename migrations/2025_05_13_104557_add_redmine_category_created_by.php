<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddRedmineCategoryCreatedBy extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('category')) {
            Schema::table('category', function (Blueprint $table) {
                if (!Schema::hasColumn('category', 'created_by')) {
                    $table->integer('created_by')->default(0)->comment('创建人ID');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('category', function (Blueprint $table) {
            //
        });
    }
}
