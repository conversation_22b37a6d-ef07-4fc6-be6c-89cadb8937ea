# usermod -aG docker gitlab-runner

stages:
  - build
  - deploy

variables:
  PROJECT_NAME: hyperf
  REGISTRY_URL: registry-docker.org

build_test_docker:
  stage: build
  before_script:
#    - git submodule sync --recursive
#    - git submodule update --init --recursive
  script:
    - docker build . -t $PROJECT_NAME
    - docker tag $PROJECT_NAME $REGISTRY_URL/$PROJECT_NAME:test
    - docker push $REGISTRY_URL/$PROJECT_NAME:test
  only:
    - test
  tags:
    - builder

deploy_test_docker:
  stage: deploy
  script:
    - docker stack deploy -c deploy.test.yml --with-registry-auth $PROJECT_NAME
  only:
    - test
  tags:
    - test

build_docker:
  stage: build
  before_script:
#    - git submodule sync --recursive
#    - git submodule update --init --recursive
  script:
    - docker build . -t $PROJECT_NAME
    - docker tag $PROJECT_NAME $REGISTRY_URL/$PROJECT_NAME:$CI_COMMIT_REF_NAME
    - docker tag $PROJECT_NAME $REGISTRY_URL/$PROJECT_NAME:latest
    - docker push $REGISTRY_URL/$PROJECT_NAME:$CI_COMMIT_REF_NAME
    - docker push $REGISTRY_URL/$PROJECT_NAME:latest
  only:
    - tags
  tags:
    - builder

deploy_docker:
  stage: deploy
  script:
    - echo SUCCESS
  only:
    - tags
  tags:
    - builder
