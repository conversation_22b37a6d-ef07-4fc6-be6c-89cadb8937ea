# Default Dockerfile
#
# @link     https://www.hyperf.io
# @document https://hyperf.wiki
# @contact  <EMAIL>
# @license  https://github.com/hyperf-cloud/hyperf/blob/master/LICENSE

FROM hyperf/hyperf:7.4-alpine-v3.15-swoole
LABEL maintainer="Hyperf Developers <<EMAIL>>" version="1.0" license="MIT" app.name="Hyperf"

##
# ---------- env settings ----------
##
# --build-arg timezone=Asia/Shanghai
ARG timezone

ENV TIMEZONE=${timezone:-"Asia/Shanghai"} \
    COMPOSER_VERSION=1.10.10 \
    # APP_ENV=prod \
    # SCAN_CACHEABLE=(true)
    #  install and remove building packages
    PHPIZE_DEPS="autoconf dpkg-dev dpkg file g++ gcc libc-dev make php7-dev php7-pear pkgconf re2c pcre-dev pcre2-dev zlib-dev libtool automake"

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories

# update
RUN set -ex \
    && apk update \
    && apk add --no-cache $PHPIZE_DEPS\
    && apk add --no-cache \
             chromium \
             nss \
             freetype \
             harfbuzz \
             ca-certificates \
             ttf-freefont \
             nodejs \
             npm \
    # install composer
    && cd /tmp \
    && wget https://mirrors.aliyun.com/composer/composer.phar \
    && chmod u+x composer.phar \
    && mv composer.phar /usr/local/bin/composer \
    # install rar extensions
    # && pecl install rar \
    # && echo "extension=rar.so" > /etc/php7/conf.d/rar.ini \
    # show php version and extensions
    && php -v \
    && php -m \
    && php --ri swoole \
    #  ---------- some config ----------
    && cd /etc/php7 \
    # - config PHP
    && { \
        echo "upload_max_filesize=128M"; \
        echo "post_max_size=128M"; \
        echo "memory_limit=1G"; \
        echo "date.timezone=${TIMEZONE}"; \
    } | tee conf.d/99_overrides.ini \
    # - config timezone
    && ln -sf /usr/share/zoneinfo/${TIMEZONE} /etc/localtime \
    && echo "${TIMEZONE}" > /etc/timezone \
    # ---------- clear works ----------
    && rm -rf /var/cache/apk/* /tmp/* /usr/share/man \
    && echo -e "\033[42;37m Build Completed :).\033[0m\n"\

# 跳过自动安装 Chrome 包. 使用上面已经安装的 Chrome
#ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
#    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Puppeteer v6.0.0 配套 Chromium 89
# RUN yarn add puppeteer@6.0.0

WORKDIR /var/www

# fix aliyun oss wrong charset: https://github.com/aliyun/aliyun-oss-php-sdk/issues/101
# https://github.com/docker-library/php/issues/240#issuecomment-762438977
# RUN apk --no-cache --allow-untrusted --repository http://dl-cdn.alpinelinux.org/alpine/edge/community/ add gnu-libiconv=1.15-r2
# ENV LD_PRELOAD /usr/lib/preloadable_libiconv.so


# RUN ["chmod", "+x", "/usr/local/etc/php/composes/hyperf/start.sh"]

# Composer Cache
# COPY ./composer.* /opt/www/
# RUN composer install --no-dev --no-scripts

# COPY . /opt/www
# RUN composer install --no-dev -o && php bin/hyperf.php

# EXPOSE 8054

# ENTRYPOINT ["php", "/var/www/station_forum/bin/hyperf.php", "start"]
# ENTRYPOINT ["php", "/var/www/tchip_ucenter/bin/hyperf.php", "start"]
# ENTRYPOINT [""]
