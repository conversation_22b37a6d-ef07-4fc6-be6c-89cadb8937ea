#应用语言 zh_CN - 中文， en - 英文
APP_LANG=zh_CN
APP_NAME=tchip_bi
APP_ENV=dev
SCAN_CACHEABLE=false

#服务端口
SERVER_HTTP_PORT=8057

#mysql 主配置
DB_DRIVER=mysql
DB_HOST=*************
DB_PORT=3306
DB_DATABASE=tchip_bi
DB_USERNAME=root
DB_PASSWORD=123
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_PREFIX=bi_
DB_MAX_CONNECTIONS=10
DB_MAX_IDLE_TIME=60

#stationpc_bbscn mysql 配置
DB_STATIONPC_BBSCN_DRIVER=mysql
DB_STATIONPC_BBSCN_HOST=*************
DB_STATIONPC_BBSCN_PORT=3306
DB_STATIONPC_BBSCN_DATABASE=stationpc_cn
DB_STATIONPC_BBSCN_USERNAME=root
DB_STATIONPC_BBSCN_PASSWORD=zstchip2102
DB_STATIONPC_BBSCN_CHARSET=utf8mb4
DB_STATIONPC_BBSCN_COLLATION=utf8mb4_unicode_ci
DB_STATIONPC_BBSCN_PREFIX=pre_

#stationpc_manager mysql 配置
DB_STATIONPC_MANAGER_DRIVER=mysql
DB_STATIONPC_MANAGER_HOST=rm-bp1rxdon0lw2z7h1dxo.mysql.rds.aliyuncs.com
DB_STATIONPC_MANAGER_PORT=3306
DB_STATIONPC_MANAGER_DATABASE=stationpc_manager
DB_STATIONPC_MANAGER_USERNAME=stationpc_manager
DB_STATIONPC_MANAGER_PASSWORD=k8UA8xIW9EA
DB_STATIONPC_MANAGER_CHARSET=utf8mb4
DB_STATIONPC_MANAGER_COLLATION=utf8mb4_unicode_ci
DB_STATIONPC_MANAGER_PREFIX=ma_

#stationpc_bbsen mysql 配置
DB_STATIONPC_BBSEN_DRIVER=mysql
DB_STATIONPC_BBSEN_HOST=***********
DB_STATIONPC_BBSEN_PORT=3306
DB_STATIONPC_BBSEN_DATABASE=stationpc_en
DB_STATIONPC_BBSEN_USERNAME=root
DB_STATIONPC_BBSEN_PASSWORD=zstchip2102
DB_STATIONPC_BBSEN_CHARSET=utf8mb4
DB_STATIONPC_BBSEN_COLLATION=utf8mb4_unicode_ci
DB_STATIONPC_BBSEN_PREFIX=pre_

#stationpc_manager_en mysql 配置
DB_STATIONPC_MANAGEREN_DRIVER=mysql
DB_STATIONPC_MANAGEREN_HOST=rm-rj971418co4rv1hh8ho.mysql.rds.aliyuncs.com
DB_STATIONPC_MANAGEREN_PORT=3306
DB_STATIONPC_MANAGEREN_DATABASE=stationpc_manager_en
DB_STATIONPC_MANAGEREN_USERNAME=stationpc_manager_en
DB_STATIONPC_MANAGEREN_PASSWORD=m4rFNNNdznQ
DB_STATIONPC_MANAGEREN_CHARSET=utf8mb4
DB_STATIONPC_MANAGEREN_COLLATION=utf8mb4_unicode_ci
DB_STATIONPC_MANAGEREN_PREFIX=ma_

#tchip_sale mysql配置
DB_TCHIP_SALE_HOST=*************
DB_TCHIP_SALE_DATABASE=tchipsale_20220727
DB_TCHIP_SALE_USERNAME=root
DB_TCHIP_SALE_PASSWORD=123
DB_TCHIP_SALE_PREFIX=dwin_

#tchip_bi_bbs mysql配置
DB_TCHIP_BBS_HOST=*************
DB_TCHIP_BBS_DATABASE=tchip_bi_bbs
DB_TCHIP_BBS_USERNAME=root
DB_TCHIP_BBS_PASSWORD=123
DB_TCHIP_BBS_PREFIX=bi_bbs_
DB_TCHIP_BBS_CHARSET=utf8mb4
DB_TCHIP_BBS_COLLATION=utf8mb4_general_ci

REDIS_HOST=*************
REDIS_AUTH=(null)
REDIS_PORT=6379
REDIS_DB=0
REDIS_PREFIX=tchip:bi:

#SSO 配置
AUTH_SSO_CLIENTS=h5,weapp
SSO_JWT_SECRET=SP3TfQJiy4R1fyA1
SIMPLE_JWT_SECRET=uWQFwuQNhcMjDeOk

##企业微信配置
#企业ID
CORPID=ww761dbc2ad3b8becd
WORKWX_TOKEN=m23ImJGLmjxyDf6j1dY3gSyNdFUk
WORKWX_ENCODING_AES_KEY=spMw5SadCGtYmLQhDqL13CnLwPR2kJoNZCeBtyXim97

#应用-数字天启
AGENT_TCHIP_BI_AGENTID=1000006
AGENT_TCHIP_BI_SECRET=sSnMjo6_HldgC7AcEsIVYlYsMsGnddK6SAoeLVHno_U

#应用-通讯录
CONTACTS_SECRET=2zGzpNv02Kc3ZaJIJAgxnUX1fJ8_Cr1GK2VbSMpdkYg

#应用-审批
APPROVAL_AGENTID=3010040
APPROVAL_SECRET=2CsMZ7qtZ52gT2ehXrSAGM0f7H3sZw4ryIjyAtxJpk8
APPROVAL_LEND_TEMPLATEID=Bs5M5h5YcCEQisyyLTTuBf7mnqtV4QGkc58PZgfdV

##微信公众号配置
#StationPC
MP_STATIONPC_APPID=wx73bcae4a67ed2074
MP_STATIONPC_SECRET=4fd1d7291a7f7be91335e411f8fcc102

##百度统计配置
TONGJI_BAIDU_USERNAME=T-Firefly
TONGJI_BAIDU_PASSWORD=Firefly2102
TONGJI_BAIDU_TOKEN=ccf23a8f542968f849a83bd6e75fad45

#redmine 配置
REDMINE_URL=http://***************:8300/
REDMINE_FILE_URL=http://***************:8063/files/

#ERP 接口地址
TCHIP_SALE_ERP_URL=http://************:8080/

##定时任务总开关，false关闭整个定时任务进程
CRONTAB_ONOFF=true
# 每周一早上10点周报填写通知
INFORM_USER_REPORT=true
# 周一至周五早上9点推送销售订单
WXWORK_PUSH_SALEORDER=true
##检测ERP产品库存变化
ERP_PRODUCT_STOCK_CHACK=true
##同步销售系统产品状态
SALE_PRODUCT_SYNC_STATUS=true
##检测IPTVBOX文件变化
STATIONPC_IPTVBOX_CHANGE=false

##邮件配置
MAIL_MAILER=smtp
MAIL_SMTP_HOST=smtp.exmail.qq.com
MAIL_SMTP_PORT=465
MAIL_SMTP_USERNAME=<EMAIL>
MAIL_SMTP_PASSWORD=hUtBevE7m8HH6cQM
MAIL_SMTP_ENCRYPTION=ssl
MAIL_SMTP_TIMEOUT=null
MAIL_SMTP_AUTH_MODE=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=数字天启通知

##stationpc_manager管理后台IPTV源文件变化通知
IPTV_NOTICE_WORKID=Feng
IPTV_NOTICE_MAIL=<EMAIL>

# BI 项目地址
BI_FRONTEND_HOST=http://bi.t-firefly.com:2101
BI_BACKEND_HOST=http://***************:8057

TCHIP_BI_BBS_URL=http://127.0.0.1:8046

UPLOAD_MIME_TYPE=jpg,png,bmp,jpeg,gif,zip,rar,xls,xlsx,wav,mp4,mp3,pdf,7z,img,txt,webp,ppt,pptx,stp,step,obj,pcb,sch
UPLOAD_MAX_SIZE=100mb

## 快递鸟APIKEY
KD_EBUSINESSID=1832210
KD_APIKEY=6a118864-aab0-41cd-949e-83188641fbc0

## 快递100APIKEY
KDH_APIKEY=thbSYrfF2472
KDH_CUSTOMER=CC563317AE45D2A4018355A1C33798DE

## RSA 公钥密钥
RSA_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCgFjmd7r2p2azvnPt4AIjJmnf1R54lnHMWQhy2ZTWIhFl+d4j5+O51HGDiFVMy8YVQUHfyJJGze2GdvbMkoR9RLbDbnk+KpXiLgrgLEhIo2yGbm+W+rUyI/cG70lNzxoV2cj8cQ64ZscE/0izFEghGltW1dcJibv18NaQSIBfi+QIDAQAB
-----END PUBLIC KEY-----"
RSA_PRIVATE_KEY="**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

## 创建产品时默认要加入的人员
DEFAULT_PRODUCT_USER_NAME="黄其勇, 文晓东, 邹学芳, 廖华明, 邓福盛, 孙贵生, 郑嘉文, 黄凤英, 戴君豪（Linux）, 黄锦成, 萧加结, 李泳琳, 高振兴, 陈桃, 陈倩仪"
