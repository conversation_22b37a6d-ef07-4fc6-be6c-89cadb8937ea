<?php

namespace App\Core\Services\TestPlan;

use App\Constants\DataBaseCode;
use App\Constants\StatusCode;
use App\Constants\TestPlanCode;
use App\Core\Services\BusinessService;
use App\Core\Services\ExcelAnalyze\Writer\ExcelWriter;
use App\Core\Services\Project\IssueService;
use App\Core\Services\Redmine\UserService;
use App\Core\Utils\Tree;
use App\Exception\AppException;
use App\Model\Redmine\EnumerationModel;
use App\Model\Redmine\IssueCategoriesModel;
use App\Model\Redmine\ProjectModel;
use App\Model\Redmine\TestCaseIssueRelationModel;
use App\Model\Redmine\TestCaseLibraryModel;
use App\Model\Redmine\TestCaseModel;
use App\Model\Redmine\TestCaseStepModel;
use App\Model\Redmine\TestDirectoryModel;
use App\Model\Redmine\TestPlanCaseModel;
use App\Model\Redmine\TestPlanCaseResultModel;
use App\Model\Redmine\TestPlanCaseStepModel;
use App\Model\Redmine\TestPlanLogModel;
use App\Model\Redmine\TestPlanModel;
use App\Model\Redmine\TestPlanUserModel;
use App\Model\Redmine\UserModel;
use Exception;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Throwable;


class   TestPlanCaseService extends BusinessService
{
    /**
     * @Inject()
     * @var TestPlanCaseModel
     */
    protected $model;


    /**
     * 新增/编辑
     * @param int $id
     * @param array $values
     * @return bool|int
     */
    public function doEdit(int $id, array $values)
    {
        $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
        $db->beginTransaction();
        try {
            if ($id > 0) {
                $row = $this->model::query()->find($id);

                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }
                $planId = $row->test_plan_id;
                $oldTestStatus = $row->test_status;
                $oldAssignedUser = $row->assigned_user;
                $result = $row->update($values);
                //测试状态
                if (!empty($values['test_status']) && $oldTestStatus != $values['test_status']) {
                    $param = [
                        'test_status' => $values['test_status'],
                        'remark'      => $values['remark'] ?? '',
                        'remark_html' => $values['remark_html'] ?? '',
                    ];
                    $this->addCaseResult($id, $param);
                }
                if(isset($values['assigned_user']) && $values['assigned_user'] != $oldAssignedUser){
                    if(!TestPlanUserModel::query()->where('test_plan_id', $planId)->where('user_id', $values['assigned_user'])->exists()){
                        TestPlanUserModel::query()->create(['test_plan_id' => $planId, 'user_id' => $values['assigned_user']]);
                    }
                    if(!$this->model::query()->where('test_plan_id', $planId)->where('assigned_user', $oldAssignedUser)->exists()){
                        TestPlanUserModel::query()->where('test_plan_id', $planId)->where('user_id', $oldAssignedUser)->delete();
                    }
                    TestPlanLogModel::query()->create([
                        'test_plan_id' => $planId,
                        'relation_id'  => $id,
                        'created_by'   => getRedmineUserId(),
                        'change_type'  => 'test_plan_case',
                        'change_field' => 'assigned_user',
                        'old_value'    => $oldAssignedUser,
                        'new_value'    => $values['assigned_user'],
                    ]);
                }
                $this->updatePlanStatus($planId);
            } else {
                $result = $this->model::query()->create($values);
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }

        return $result;
    }

    /**
     * 获取计划事项列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return array|mixed
     */
    public function getIssueList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        if (empty($filter['test_plan_id'])) return [];
        $testPlanId = $filter['test_plan_id'];
        unset($filter['test_plan_id']);
        //获取事项id
        $issueId = $this->model::query()->where('test_plan_id', $testPlanId)
            ->join('test_case_issue_relation', function ($join) {
                $join->on('test_case_issue_relation.test_case_id', '=', 'test_plan_case.test_case_id')
                    ->whereNull('test_case_issue_relation.deleted_at');
            })
            ->pluck('test_case_issue_relation.issue_id')->toArray();
        if (empty($issueId)) return [];
        $filter['id'] = implode(',', $issueId);
        $op['id'] = 'in';
        return make(IssueService::class)->getList($filter, $op, $sort, $order, $limit);
    }

    public function getIssueId($planId)
    {
        $issueId = $this->model::query()->where('test_plan_id', $planId)
            ->join('test_case_issue_relation', function ($join) {
                $join->on('test_case_issue_relation.test_case_id', '=', 'test_plan_case.test_case_id')
                    ->whereNull('test_case_issue_relation.deleted_at');
            })
            ->pluck('test_case_issue_relation.issue_id')->toArray();
        return $issueId;
    }

    /**
     * 批量更新
     * @param $ids
     * @param $params
     * @return bool
     */
    public function doMulti($ids, $params): bool
    {
        if (empty($ids) || empty($params) || empty($params['type'])) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
        }
        if (!is_array($ids)) {
            $ids = explode(',', $ids);
        }
        $count = 0;
        $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
        $db->beginTransaction();
        try {
//            $items = $this->model::query()->whereIn('id', explode(',', $ids))->get();
            switch ($params['type']) {
                case 'test_status':// 测试状态
                    if (empty($params['test_status'])) {
                        throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
                    }
                    foreach ($ids as $id) {
                        $result = $this->addCaseResult($id, ['test_status' => $params['test_status']]);
                        $result && $count++;
                    }
                    break;
                case 'assigned_user':  // 指派人
                    if (empty($params['assigned_user'])) {
                        throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
                    }
                    $items = $this->model::query()->whereIn('id',  $ids)->get();
                    $oldAssignUser = [];
                    foreach ($items as $item) {
                        $oldAssignUser[] = $item->assigned_user;
                        $count += $item->update(['assigned_user' => $params['assigned_user']]);
                        TestPlanLogModel::query()->create([
                            'test_plan_id' => $item->test_plan_id,
                            'relation_id'  => $item->id,
                            'created_by'   => getRedmineUserId(),
                            'change_type'  => 'test_plan_case',
                            'change_field' => 'assigned_user',
                            'old_value'    => $item->assigned_user,
                            'new_value'    => $params['assigned_user'],
                        ]);
                    }
                    $planId = $items->first()->test_plan_id ?? 0;
                    if (!TestPlanUserModel::query()->where('test_plan_id', $planId)->where('user_id', $params['assigned_user'])->exists()) {
                        TestPlanUserModel::query()->create([
                            'test_plan_id' => $planId,
                            'user_id'      => $params['assigned_user']
                        ]);
                    }
                    //测试用户
                    $caseAssignedUser = TestPlanCaseModel::query()->where('test_plan_id', $planId)->pluck('assigned_user')->toArray();
                    $deleteUsers = array_diff($oldAssignUser, $caseAssignedUser);
                    if (!empty($deleteUsers)) {
                        TestPlanUserModel::query()->whereIn('user_id', $deleteUsers)->where('test_plan_id', $planId)->delete();
                    }
                    break;
                case 'test_plan_id':   // 移动用例到其他计划
                    if (empty($params['test_plan_id'])) {
                        throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
                    }
                    foreach ($ids as $id) {
                        $result = $this->moveCaseToPlan($id, $params);
                        $result && $count++;
                    }
                    break;
                default:
                    break;
            }
            $db->commit();
        } catch (Throwable $e) {
            $db->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
        if ($count > 0) {
            return true;
        } else {
            throw new AppException(StatusCode::ERR_SERVER, __('common.No_rows_were_updated'));
        }
    }

    //添加测试结果
    public function addCaseResult($id, $params)
    {
        $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
        $db->beginTransaction();
        try {
            if (empty($params['test_status'])) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
            }
            $row = $this->model::query()->find($id);
            if (!$row) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
            }
            $planId = $row->test_plan_id;
            $row->update(['test_status' => $params['test_status']]);
            $insert = [
                'test_plan_case_id' => $id,
                'test_status'       => $params['test_status'],
                'remark'      => $params['remark'] ?? null,
                'remark_html' => $params['remark_html'] ?? null,
                'created_by'  => !empty($params['created_by']) ? $params['created_by'] : getRedmineUserId(),
            ];
            $result = TestPlanCaseResultModel::query()->create($insert);
            //添加步骤结果
            if (!empty($params['plan_case_step']) && !empty($result->id)) {
                foreach ($params['plan_case_step'] as $step) {
                    $step['plan_case_result_id'] = $result->id;
                    TestPlanCaseStepModel::query()->create($step);
                }
            }
            //增加执行次数
            $this->increaseCaseTestCount($id);
            //反改计划的状态
            $this->updatePlanStatus($planId);
            $db->commit();
        } catch (Throwable $e) {
            $db->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
        return $result;
    }

     /**
     * 保存测试计划用例步骤结果
     * @param int $test_plan_case_id 测试计划用例ID
     * @param array $params 参数
     * @return bool
     */
    public function saveStepResult($test_plan_case_id, $params)
    {
        $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
        $db->beginTransaction();
        
        try {
            // 验证测试计划用例是否存在
            $planCase = $this->model::query()->find($test_plan_case_id);
            if (!$planCase) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
            }
            
            // 验证步骤数据
            if (empty($params['plan_case_step']) || !is_array($params['plan_case_step'])) {
                throw new AppException(StatusCode::ERR_SERVER, '步骤数据不能为空');
            }
            
            // 获取该测试计划用例的最新测试结果记录
            $latestResult = TestPlanCaseResultModel::query()
                ->where('test_plan_case_id', $test_plan_case_id)
                ->orderBy('created_at', 'desc')
                ->first();
            
            // 如果没有测试结果记录，创建一个临时的结果记录用于存储步骤结果
            if (!$latestResult) {
                $latestResult = TestPlanCaseResultModel::query()->create([
                    'test_plan_case_id' => $test_plan_case_id,
                    'test_status' => 0, // 临时状态，表示未完成测试
                    'remark' => null,
                    'remark_html' => null,
                    'created_by' => !empty($params['created_by']) ? $params['created_by'] : getRedmineUserId(),
                ]);
            }
            
            // 处理步骤结果
            foreach ($params['plan_case_step'] as $step) {
                // 验证步骤数据
                if (empty($step['base_step_id'])) {
                    continue;
                }
                
                // 查找是否已存在该步骤的结果
                $existingStepResult = TestPlanCaseStepModel::query()
                    ->where('plan_case_result_id', $latestResult->id)
                    ->where('base_step_id', $step['base_step_id'])
                    ->first();
                
                $stepData = [
                    'plan_case_result_id' => $latestResult->id,
                    'test_plan_case_id' => $step['test_plan_case_id'] ?? $test_plan_case_id,
                    'base_step_id' => $step['base_step_id'],
                    'actual_result' => $step['actual_result'] ?? null,
                    'result_status' => $step['result_status'] ?? 0,
                ];
                
                if ($existingStepResult) {
                    // 更新现有步骤结果
                    $existingStepResult->update($stepData);
                } else {
                    // 创建新的步骤结果
                    TestPlanCaseStepModel::query()->create($stepData);
                }
            }
            
            $db->commit();
            return true;
            
        } catch (Throwable $e) {
            $db->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    //增加执行次数
    public function increaseCaseTestCount($idArr)
    {
        if (!is_array($idArr)) {
            $idArr = explode(',', $idArr);
        }
        $idArr = array_unique($idArr);
        $this->model::query()->whereIn('id', $idArr)->increment('execution_count');
    }

    /**
     * 根据用例的测试状态来修改计划的状态
     * @param $planId
     * @return void
     */
    public function updatePlanStatus($planId)
    {
        //获取计划下的用例
        $planRow = TestPlanModel::query()->find($planId);
        if (!$planRow) return;
        $cases = $this->model::query()->where('test_plan_id', $planId)->get();
        $cases = $cases ? $cases->toArray() : [];
        if (empty($cases)) {
            $updateStatus = TestPlanCode::STATUS_NOT_START;
        } else {
            $casesStatus = array_column($cases, 'test_status');
            $casesStatusCount = array_count_values($casesStatus);
            $startedCaseCount = ($casesStatusCount[TestPlanCode::CASE_STATUS_PASS] ?? 0)
                + ($casesStatusCount[TestPlanCode::CASE_STATUS_IGNORE] ?? 0)
                + ($casesStatusCount[TestPlanCode::CASE_STATUS_RESTART] ?? 0)
                + ($casesStatusCount[TestPlanCode::CASE_STATUS_STOP] ?? 0)
                + ($casesStatusCount[TestPlanCode::CASE_STATUS_FAIL] ?? 0);

            if (($casesStatusCount[TestPlanCode::CASE_STATUS_NOT_START] ?? 0) == count($cases)) {
                //全部未测
                $updateStatus = TestPlanCode::STATUS_NOT_START;
            } elseif ($startedCaseCount == count($cases)) {
                //全部修改过
                $updateStatus = TestPlanCode::STATUS_FINISHED;
            } else {
                $updateStatus = TestPlanCode::STATUS_ON_GOING;
            }
        }
        if ($planRow->status != $updateStatus) {
            $planRow->status = $updateStatus;
            $planRow->save();
        }
    }
    public function moveCaseToPlan($id, $params)
    {
        //复制
        $row = $this->model::query()->find($id);
        if (!$row) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
        }
        $new = [
            'test_plan_id' => $params['test_plan_id'],
            'test_case_id' => $row->test_case_id,
        ];
        //删除
        $this->doDelete($id);
        if($this->model::query()->where($new)->exists()){
            return true;
        }
        $result =  $this->model::query()->create($new);
        //修改测试计划状态
        $this->updatePlanStatus($id);
        return $result;
    }

    public function doDelete($ids): int
    {
        $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
        $db->beginTransaction();
        try {
            $ids = !is_array($ids) ? explode(',', $ids) : $ids;
            $caseList = $this->model::query()->whereIn('id', $ids)->get();
            $planId = $caseList[0]->test_plan_id??0;
            foreach ($caseList as $case) {
                $oldAssignUser[] = $case->assigned_user;
                TestPlanCaseResultModel::query()->where('test_plan_case_id', $case->id)->delete();
                TestPlanCaseStepModel::query()->where('test_plan_case_id', $case->id)->delete();
            }
            $result = $this->model::destroy($ids);
            //测试用户
            $planUser = TestPlanCaseModel::query()->where('test_plan_id', $planId)->pluck('assigned_user')->toArray();
            $deleteUsers = array_diff($oldAssignUser, $planUser);
            if (!empty($deleteUsers)) {
                TestPlanUserModel::query()->whereIn('user_id', $deleteUsers)->where('test_plan_id', $planId)->delete();
            }
            //修改测试计划状态
            $this->updatePlanStatus($planId);
            $db->commit();
            return $result;
        } catch (Exception $e) {
            $db->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function export(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        $data = $this->getAllList($filter, $op, $sort, $order, 999999);
        $data = !empty($data['data']['list']) ? $data['data']['list'] : [];
        $filename = "测试计划用例.xls";
        $sheetName = 'sheet1';
        $excelWriter = new ExcelWriter($filename, $sheetName);
        $titleData = [
            [
                '序号',
                '测试计划',
                '测试结果',
                '分配人',
                '执行次数',
                //////////////////// 用例导入文件必要字段
                '模块',
                '目录',
                '标题',
                '描述',
                '步骤描述',
                '文本描述',
                '优先级',
                '用例版本',
                '所属项目',
                '评估工时',
                '创建人',
                '相关事项',
                //////////////////// end
                '关联事项数',
            ]
        ];

        $excelWriter->addData($titleData);

        $excelWriter->setColumnWidth(7, 36); // 设置“目录”列
        $excelWriter->setColumnWidth(8, 100); // 设置“标题”列（第8列）宽度
        $excelWriter->setColumnWidth(9, 36); // 前置条件描述列
        $excelWriter->setColumnWidth(10, 50); // 步骤描述列
        $excelWriter->setColumnWidth(11, 50); // 文本描述列
        $excelWriter->setColumnWidth(17, 50); // 相关事项列

        $planName = '';
        if (!empty($data[0]['test_plan_id'])) {
            $planName = TestPlanModel::query()->where('id', $data[0]['test_plan_id'])->value('title');
        }

        $caseIds = array_column($data, 'test_case_id');
        $casesData= TestCaseModel::query()->whereIn('id', $caseIds)->get()->toArray();
        $preconditions = array_column($casesData, 'precondition', 'id'); // 前置条件
        $descriptions = array_column($casesData, 'description', 'id'); // 文本类型-文本描述
        $expectedResults = array_column($casesData, 'expected_result', 'id'); // 文本类型-预期结果
        $estimatedHourList = array_column($casesData, 'estimated_hours', 'id'); // 评估工时
        $projectId = $casesData[0]['project_id'];
        $projectName = ProjectModel::query()->where('id', $projectId)->value('name');

        // 创建人
        $createdUserIds = array_column($casesData, 'created_by' );
        $authorList = UserModel::query()->whereIn('id', $createdUserIds)
            ->select('id', 'firstname', 'lastname')
            ->selectRaw("CONCAT(firstname, ' ', lastname) as name")
            ->get()
            ->toArray();
        $authorList = array_column($authorList, 'name', 'id');
        $createdByList = array_column($casesData, 'created_by', 'id');
        foreach ($createdByList as &$item) {
            $item = $authorList[$item] ?? '';
        }


        // 相关事项
        $domain = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
        $RelationIssues = TestCaseIssueRelationModel::query()
            ->whereIn('test_case_id', $caseIds)
            ->get(['test_case_id', 'issue_id'])
            ->groupBy('test_case_id')
            ->mapWithKeys(function ($group, $caseId) use ($domain, $projectId) {
                // 将每个 test_case_id 对应的 URL 构建成数组
                $urls = $group->pluck('issue_id')->map(function ($issueId) use ($domain, $projectId) {
                    return $domain . '/#/project/detail?project_id=' . $projectId . '&issue_id=' . $issueId;
                })->toArray();
                return [$caseId => $urls];
            })
            ->toArray();

        // 步骤描述
        $steps = TestCaseStepModel::query()->whereIn('test_case_id', $caseIds)->get()->groupBy('test_case_id')->toArray();

        $row = [];
        foreach ($data as $k => $v) {
            //获取目录
            $index = ++$k;

            // 将相关事项的 URL 用换行符拼接为字符串
            $relationIssues = $RelationIssues[$v['test_case_id']] ?? []; // 获取相关事项数组
            $relationIssuesString = implode("\n", $relationIssues);

            // 步骤描述拼接
            $stepsString = '';
            if (!empty($steps[$v['test_case_id']])) {
                $stepIndex = 1; // 步骤的序号
                foreach ($steps[$v['test_case_id']] as $step) {
                    $stepsString .= $stepIndex . '. ' . $step['step'] . '->' . $step['expected_result'] . "\n";
                    $stepIndex++;
                }
                $stepsString = rtrim($stepsString); // 去除末尾多余的换行符
            }

            // 文本描述拼接
            $descriptionsString = '';
            if ( $v['desc_type'] == 1 && !empty($descriptions[$v['test_case_id']])) {
                $descriptionsString = $descriptions[$v['test_case_id']] . '->' . $expectedResults[$v['test_case_id']];
            }

            // 前置条件 去除html标签
            $html = $preconditions[$v['test_case_id']];
            // 替换 <p> 标签为换行符
            $html = preg_replace('/<p.*?>/', "\n", $html);
            // 去掉最开始可能产生的换行符
            $html = ltrim($html, "\n");
            // 替换 <br> 标签为换行符
            $html = preg_replace('/<br\s*\/?>/', "\n", $html);
            // 去除剩余的 HTML 标签，保留换行符
            $preconditionText = strip_tags($html);

            $row = [[
                $index, // 序号
                $planName, // 测试计划
                $v['test_status_name'], // 测试结果
                $v['assigned_user_name'], // 分配人
                $v['execution_count'], // 执行次数

                $v['category_text']['name'] ?? '', // 模块
                $v['directory_name'], // 目录
                $v['title'], // 标题
                //$preconditions[$v['test_case_id']], // 描述
                $preconditionText, // 前置条件 （描述）
                $stepsString, // 步骤描述
                $descriptionsString, // 文本描述
                $v['priority_name'], // 优先级
                $v['library_name'], // 用例版本
                $projectName, // 所属项目
                $estimatedHourList[$v['test_case_id']], // 评估工时
                $createdByList[$v['test_case_id']]?? '', // 创建人
                $relationIssuesString, // 相关事项

                $v['issues_count'], // 关联事项数
            ]];
            $excelWriter->addData($row);
        }
        $result =  $excelWriter->download();
        $excelWriter->close();
        return $result;
    }

    /**
     * 获取列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return mixed
     */
    public function getAllList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);

        /* 
            特殊处理 assigned_user = "0" 的情况
            原因：BussinessService中的问题
            case 'IN':
                // $v 的值是字符串"0"，empty("0")返回true，!empty("0")返回false，条件不满足whereIn 不会执行并且筛选条件被忽略
                if (!empty($v)) {
                    $query = $query->whereIn($k, explode(',', $v));
                }
                break;
        */
        if (isset($filter['test_plan_case.assigned_user']) && $filter['test_plan_case.assigned_user'] === '0') {
            $query = $query->where('test_plan_case.assigned_user', 0);
        }

        $filed = [
            'test_plan_case.*',
            'test_case.title',
            'test_case.priority',
            'test_case.library_id',
            'test_case.directory_id',
            'test_case.category_id',
            'test_case.desc_type',
            'test_case.sort_order as case_sort_order', // 明确选择 test_case 的 sort_order 字段
        ];
        $query = $query->join('test_case', function ($join) {
            $join->on('test_plan_case.test_case_id', '=', 'test_case.id')
                ->whereNull('test_case.deleted_at');
        });
        $query = $query->join('test_directory', function ($join) {
            $join->on('test_case.directory_id', '=', 'test_directory.id')
                ->whereNull('test_directory.deleted_at');
        });
        $query = $query->select($filed);

        $paginate = $query
            ->orderBy('case_sort_order', 'desc') // 使用 test_case 表的 sort_order 进行排序
            ->orderBy($sort, $order)
        ->paginate($limit, ['*'], 'pageNo')
        ->toArray();

        $categoryIds = array_column(array_filter($paginate['data'], function ($item) {
            return  isset($item['category_id']);
        }), 'category_id');
        $categoryIds = array_unique($categoryIds);
        $categories = IssueCategoriesModel::whereIn('id', $categoryIds)->get()->keyBy('id');
        $categories = $categories ? $categories->toArray() : [];

        foreach ($paginate['data'] as &$item) {
            if ( !empty($categories)) {
                // 根据 category_id 从预加载的 categories 集合中找到对应的 category_text
                $item['category_text'] = $item['category_id'] ? ($categories[$item['category_id']] ?? null) : null;
            }
            //$item['author_text'] = $item['created_by'] ? ($authors[$item['created_by']] ?? null) : null;
        }

        $list = $paginate['data'];

        //$list = $query
        //    ->orderBy($sort, $order)->get()->toArray();


        //获取库
        $libraryIds = array_unique(array_column($list, 'library_id'));
        $libraries = TestCaseLibraryModel::query()->whereIn('id', $libraryIds)->select(['name', 'id'])->orderBy('id', 'desc')->get()->toArray();
        //获取目录
        $directoryIds = array_unique(array_column($list, 'directory_id'));
        $directoryPath = TestDirectoryModel::query()->whereIn('id', $directoryIds)->pluck('path','id')->toArray();
        $directoryPIds = array_unique(array_filter(explode('-', implode('-', $directoryPath))));
        $directoryIds = array_merge($directoryIds, $directoryPIds);
        $directories = TestDirectoryModel::query()->whereIn('id', $directoryIds)->select(['title', 'id', 'parent_id', 'library_id', 'path', 'sort_order'])->get()->toArray();
        //获取用户
        $userIds = array_unique(array_column($list, 'assigned_user'));
        $users = make(UserService::class)->getUserName($userIds);
        //获取优先级
        $priorityIds = array_unique(array_column($list, 'priority'));
        $priorities = EnumerationModel::query()->whereIn('id', $priorityIds)->pluck('name', 'id')->toArray();


        // 提取需要查询的 test_case_id 列表
        $testCaseIds = array_column($list, 'test_case_id');
        // 批量查询 issues_count
        $issuesCounts = TestCaseIssueRelationModel::query()
            ->whereIn('test_case_id', $testCaseIds)
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('issues')
                    ->whereColumn('issues.id', 'test_case_issue_relation.issue_id');
                    //->whereNull('issues.deleted_at'); // 排除已删除的 issue
            })
            ->selectRaw('test_case_id, COUNT(*) as issues_count')
            ->groupBy('test_case_id')
            ->pluck('issues_count', 'test_case_id'); // 使用 pluck 将结果以键值对形式返回

        $libraryNames = array_column($libraries, 'name', 'id');
        $directoryPaths = array_column($directories, 'path', 'id');
        $directoryTitles = array_column($directories, 'title', 'id');
            // 获取列表内容
        foreach ($list as &$item) {
            // 分配人
            $item['assigned_user'] = $item['assigned_user'] ?: null;
            $item['assigned_user_name'] = $users[$item['assigned_user']] ?? '';

            // 优先级
            $item['priority_name'] = $priorities[$item['priority']] ?? '';

            // 测试结果
            $item['test_status_name'] = TestPlanCode::CASE_STATUS_MAP[$item['test_status']] ?? '';

            // 从批量查询结果中获取 issues_count
            $item['issues_count'] = $issuesCounts[$item['test_case_id']] ?? 0;

            // 库名称
            $item['library_name'] = $libraryNames[$item['library_id']] ?? '';

            // 目录名称
            $dirPath = $directoryPaths[$item['directory_id']] ?? '';
            $item['directory_name'] = $directoryTitles[$item['directory_id']] ?? '';
            if ($dirPath) {
                // 分割路径并构建目录名称
                $dirPathArr = explode('-', $dirPath);
                $directoryNames = array_map(function ($dirId) use ($directoryTitles) {
                    return $directoryTitles[$dirId] ?? '';
                }, $dirPathArr);
                $item['directory_name'] = implode('-', $directoryNames) . '-' . $directoryTitles[$item['directory_id']];
            }
        }

        //转换目录成树状结构
        $tree = [];
        //
        //foreach ($libraries as $library) {
        //    $tempDir = array_filter($directories, function ($item) use ($library) {
        //        return $item['library_id'] == $library['id'];
        //    });
        //    $tempList = array_filter($list, function ($item) use ($library) {
        //        return $item['library_id'] == $library['id'];
        //    });
        //    $library['children'] = make(Tree::class)->getTreeListV5($tempDir, $tempList);
        //    $library['type'] = 'library';
        //    $tree[] = $library;
        //}

        // 初始化平铺后的树结构数组
        $flatTree = [];

        $flatDir = array_column($directories, null, 'id');

        // 用以处理版本、目录和用例之间的关系
        $casesByDir = []; // 用于暂存用例，键为目录的 ID

        foreach ($libraries as $library) {
            // 对应版本下的目录
            $tempDir = array_filter($directories, function ($item) use ($library) {
                return $item['library_id'] == $library['id'];
            });

            $tempDir = collect($tempDir)->sortBy(function ($item) use ($sort) {
                $sortOrder = $item['sort_order'];
                $secondarySort = ($sort != 'id' && isset($item[$sort])) ? $item[$sort] : null;
                return [$sortOrder, $secondarySort];
            }, SORT_REGULAR, $order == 'desc' || $order == 'DESC')->values()->toArray();

            // 对应版本下的用例
            $tempList = array_filter($list, function ($item) use ($library) {
                return $item['library_id'] == $library['id'];
            });

            // 1. 平铺版本信息
            $library['parent_id'] = null; // 版本的 parent_id 为 null
            $library['key_id'] = 'library_' . $library['id']; // 生成唯一 id
            $library['title'] = $library['name'];
            $library['type'] = 'library';
            $flatTree[] = $library;

            // 2. 平铺目录信息
            foreach ($tempDir as $dir) {
                // 提前将 tempList 用例按目录 id 分类并存储到 casesByDir 中 以保证后续加入设置子目录位置始终在子用例之上
                $casesByDir[$dir['id']] = array_filter($tempList, function ($item) use ($dir) {
                    return $item['directory_id'] == $dir['id'];
                });

                // 生成唯一 id 并设置 parent_id
                $dirParentId = $flatDir[$dir['id']]['parent_id'];
                $dir['parent_id'] = $flatDir[$dir['id']]['parent_id'] == 0 ? $library['key_id'] : 'dir_' . $dirParentId;
                $dir['key_id'] = 'dir_' . $dir['id'];
                $dir['type'] = 'directory';
                $flatTree[] = $dir;
            }
        }

        // 3.插入用例信息到对应目录
        foreach ($casesByDir as $dirId => $dirCases) {
            foreach ($dirCases as $tItem) {
                // 确保用例的 parent_id 正确，并生成唯一 id
                $tItem['parent_id'] = 'dir_' . $dirId; // 用例的 parent_id 为对应目录的 key_id
                $tItem['case_id'] = $tItem['id'];
                $tItem['key_id'] = 'case_' . $tItem['id'];
                $tItem['type'] = 'case';
                $flatTree[] = $tItem;
            }
        }


        //// 处理版本、目录和用例之间的关系
        //foreach ($libraries as $library) {
        //    // 对应版本下的目录
        //    $tempDir = array_filter($directories, function ($item) use ($library) {
        //        return $item['library_id'] == $library['id'];
        //    });
        //    // 使用 Collection 对目录数据按 sort_order 字段进行排序，并且在 $sort != 'id' 时按指定的 $sort 字段排序
        //    $tempDir = collect($tempDir)->sortBy(function ($item) use ($sort) {
        //        // 先排序按 sort_order 字段
        //        $sortOrder = $item['sort_order'];
        //
        //        // 如果 $sort 不是 'id'，则按照 $sort 字段进行排序
        //        $secondarySort = ($sort != 'id' && isset($item[$sort])) ? $item[$sort] : null;
        //
        //        return [$sortOrder, $secondarySort];  // 先按 sort_order 排序，再按 $sort 字段排序
        //    }, SORT_REGULAR, $order == 'desc' || $order == 'DESC')->values()->toArray();
        //
        //    // 对应版本下的用例
        //    $tempList = array_filter($list, function ($item) use ($library) {
        //        return $item['library_id'] == $library['id'];
        //    });
        //
        //    // 1. 平铺版本信息
        //    $library['parent_id'] = null; // 版本的 parent_id 为 0
        //    $library['key_id'] = 'library_' . $library['id']; // 生成唯一 id
        //    $library['title'] = $library['name'];
        //    $library['type'] = 'library';
        //    $flatTree[] = $library;
        //
        //    // 2. 平铺目录信息
        //    foreach ($tempDir as $dir) {
        //        // 提前将 tempList 用例按目录 id 分类，以减少重复查找
        //        $dirCases = array_filter($tempList, function ($item) use ($dir) {
        //            return $item['directory_id'] == $dir['id']; // 用例的 directory_id 对应当前目录的 id
        //        });
        //
        //        // 生成唯一 id 并设置 parent_id
        //        $dirParentId = $flatDir[$dir['id']]['parent_id'];
        //        $dir['parent_id'] = $flatDir[$dir['id']]['parent_id'] == 0 ? $library['key_id'] : 'dir_' .$dirParentId; // 目录的 parent_id 为该版本的 id
        //        $dir['key_id'] = 'dir_' . $dir['id']; // 生成唯一 id
        //        $dir['type'] = 'directory';
        //        $flatTree[] = $dir;
        //
        //        // 3. 平铺用例信息
        //        foreach ($dirCases as $tItem) {
        //            // 确保用例的 parent_id 正确，并生成唯一 id
        //            $tItem['parent_id'] = $dir['key_id']; // 用例的 parent_id 为该目录的 id
        //            $tItem['case_id'] = $tItem['id'];
        //            $tItem['key_id'] = 'case_' . $tItem['id']; // 生成唯一 id
        //            $tItem['type'] = 'case';
        //            $flatTree[] = $tItem;
        //        }
        //    }
        //
        //}

        $tree = $flatTree;

        $paginate['data'] = [
            'list' => $list,
            'tree' => $tree
        ];
        return $paginate;
    }

    //获取结果记录
    public function getTestResult($planCaseId)
    {
        $list = TestPlanCaseResultModel::query()->with(['createdUser'])->where('test_plan_case_id', $planCaseId)->orderByDesc('id')->get()->toArray();

        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'id'                => $item['id'],
                'test_plan_case_id' => $item['test_plan_case_id'],
                'type'              => 'test_status',
                'test_status'       => $item['test_status'],
                'remark'            => $item['remark'],
                'remark_html'       => $item['remark_html'],
                'created_at'        => $item['created_at'],
                'created_by'        => $item['created_by'],
                'create_user_name'  => $item['created_user']['name'] ?? '',
                'thumb_avatar'      => $item['created_user']['thumb_avatar'] ?? '',
            ];
            if (!empty($item['remark'])) {
                $data[] = [
                    'id'                => $item['id'],
                    'test_plan_case_id' => $item['test_plan_case_id'],
                    'type'              => 'remark',
                    'test_status'       => $item['test_status'],
                    'remark'            => $item['remark'],
                    'remark_html'       => $item['remark_html'],
                    'created_by'        => $item['created_by'],
                    'created_at'        => $item['created_at'],
                    'create_user_name'  => $item['created_user']['name'] ?? '',
                    'thumb_avatar'      => $item['created_user']['thumb_avatar'] ?? '',
                ];
            }
        }
        return $data;
    }

    //获取最新结果的步骤
    public function getLatestStepResult($planCaseId)
    {
        $latestResultId = TestPlanCaseResultModel::query()->where('test_plan_case_id', $planCaseId)->max('id');
        $latestResult = TestPlanCaseStepModel::query()->where('plan_case_result_id', $latestResultId)->get()->toArray();
        return $latestResult;
    }


    public function getTestPlanCaseLog(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        $query = TestPlanLogModel::query();
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $query);

        $paginate = $query->with(['userInfo'])
            ->orderBy($sort, $order)->paginate($limit)->toArray();
        if ($paginate['data']) {
            foreach ($paginate['data'] as &$item) {
                $item['type'] = $item['change_field'];
                $item['create_user_name'] = $item['user_info']['name'] ?? '';

                if ($item['change_type'] == 'test_plan_case' && $item['change_field'] == 'assigned_user') {
                    $userInfo = UserModel::query()->find($item['new_value']??0);
                    $item['new_value_name'] = $userInfo ? ($userInfo->lastname . $userInfo->firstname): '';
                }
            }
        }

        return $paginate;
    }
}