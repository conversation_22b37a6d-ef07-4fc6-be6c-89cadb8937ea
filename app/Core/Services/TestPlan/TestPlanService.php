<?php

namespace App\Core\Services\TestPlan;

use App\Constants\DataBaseCode;
use App\Constants\StatusCode;
use App\Constants\TestPlanCode;
use App\Core\Services\BusinessService;
use App\Core\Services\Redmine\UserService;
use App\Core\Utils\Tree;
use App\Exception\AppException;
use App\Model\Redmine\TestCaseLibraryModel;
use App\Model\Redmine\TestCaseModel;
use App\Model\Redmine\TestDirectoryModel;
use App\Model\Redmine\TestPlanCaseModel;
use App\Model\Redmine\TestPlanLogModel;
use App\Model\Redmine\TestPlanModel;
use App\Model\Redmine\TestPlanUserModel;
use App\Model\Redmine\UserModel;
use App\Model\Redmine\VersionModel;
use App\Model\Redmine\FilesDocProductVersionModel;
use App\Model\Redmine\ProjectsExtModel;
use Exception;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;


class   TestPlanService extends BusinessService
{
    /**
     * @Inject()
     * @var TestPlanModel
     */
    protected $model;

    /**
     * 获取列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return mixed
     */
    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);

        $filed = [
            'test_plan.*',
            //测试用例id
            Db::raw('group_concat(test_case.id) as test_case_ids'),
            //测试计划用例id
            Db::raw('group_concat(test_plan_case.id) as test_plan_case_ids'),
            //测试用户
            Db::raw('group_concat(test_plan_user.user_id) as test_user_ids'),
            // 测试用例id的数量
            Db::raw('count(distinct test_case.id) as test_case_count'),
            //// 测试计划用例id的数量
            //Db::raw('count(distinct test_plan_case.id) as test_plan_case_count'),

        ];
        $query->leftJoin('test_plan_case', function ($join) {
            $join->on('test_plan_case.test_plan_id', '=', 'test_plan.id')
                ->whereNull('test_plan_case.deleted_at');
        })
            ->leftJoin('test_case', function ($join) {
                $join->on('test_case.id', '=', 'test_plan_case.test_case_id')
                    ->whereNull('test_case.deleted_at');
            })
            ->leftJoin('test_plan_user', function ($join) {
                $join->on('test_plan_user.test_plan_id', '=', 'test_plan.id')
                    ->whereNull('test_plan_user.deleted_at');
            })
            ->select($filed);

        $paginate = $query
            ->groupBy('test_plan.id')
            ->orderBy($sort, $order)->paginate($limit)->toArray();
        //汇总查出用户名称
        //测试用户有用作筛选条件，所以测试用户需要重新查询
        $planIdArr = array_column($paginate['data'], 'id');
        $planUserArr = TestPlanUserModel::query()->whereIn('test_plan_id', $planIdArr)
            ->groupBy(['test_plan_id'])->select([
                'test_plan_id',
                Db::raw('GROUP_CONCAT(user_id) as test_user_ids')
            ])->pluck('test_user_ids', 'test_plan_id')->toArray();
        $planUser = explode(',', implode(',', array_values($planUserArr)));
        $createUser = array_column($paginate['data'], 'created_by');
        $userIdArr = array_unique(array_filter(array_merge($planUser, $createUser)));
        $userData = make(UserService::class)->getUserName($userIdArr);

        // 提取所有的 test_plan_id
        $testPlanIds = array_column($paginate['data'], 'id');
        //批量查询test_case_status数据
        $testCaseStatuses = TestPlanCaseModel::query()
            ->leftJoin('test_case', 'test_plan_case.test_case_id', '=', 'test_case.id')
            ->whereNull('test_case.deleted_at')
            ->whereIn('test_plan_case.test_plan_id', $testPlanIds)
            ->get(['test_plan_case.test_plan_id', 'test_plan_case.test_status', 'test_plan_case.assigned_user'])
            ->groupBy('test_plan_id')
            ->mapWithKeys(function ($items, $key) use ($filter) {
                // 如果存在分配用户的筛选条件
                if (!empty($filter['test_plan_case.assigned_user'])) {
                    $items = $items->filter(function ($item) use ($filter) {
                        return $item['assigned_user'] == $filter['test_plan_case.assigned_user'];
                    });
                }
                // 返回键值对形式的结果
                return [$key => $items->pluck('test_status')->toArray()];
            });

        foreach ($paginate['data'] as &$value) {
            $value['test_case_ids'] = array_unique(array_filter(explode(',', $value['test_case_ids'])));
            $value['test_plan_case_ids'] = array_unique(array_filter(explode(',', $value['test_plan_case_ids'])));
            $value['test_case_status'] = $testCaseStatuses[$value['id']] ?? [];
            //人员名称
            $value['created_by_name'] = $userData[$value['created_by']] ?? '';
            $testUserIds = array_unique(explode(',', $planUserArr[$value['id']] ?? ''));
            $value['test_user_name'] = implode('、', array_filter(array_map(function ($id) use ($userData) {
                return $userData[$id] ?? '';
            }, $testUserIds)));
            //状态
            $value['status_name'] = TestPlanCode::STATUS_MAP[$value['status']] ?? '';
            //用例数
            $value['case_num'] = $value['test_case_count'];
            //测试进度
            $value['test_progress'] = $this->getTestPlanProgress($value['test_case_status']);
            //通过数
            $value['pass_num'] = array_count_values($value['test_case_status'])[TestPlanCode::CASE_STATUS_PASS] ?? 0;
            //通过率
            $value['pass_rate'] = calculateRate($value['pass_num'], $value['case_num'], 2, true, true);

            //$value['pass_rate'] = number_format(calculateRate($value['pass_num'], $value['case_num'], 0), 2);

            //测试周期
            $value['begin_date_text'] = $value['begin_date'] ? date('m.d', strtotime($value['begin_date'])) : '';
            $value['end_date_text'] = $value['end_date'] ? date('m.d', strtotime($value['end_date'])) : '';
            $value['test_cycle'] = $value['begin_date_text'] . '~' . $value['end_date_text'];
        }

        return $paginate;
    }

    //获取进度条数据
    public function getTestPlanProgress($planTestStatus)
    {
        $statusCount = array_count_values($planTestStatus);
        $statusArr = TestPlanCode::CASE_STATUS_MAP;
        $result = [];
        foreach ($statusArr as $key => $value) {
            $result[] = [
                'status'      => $key,
                'num'         => $statusCount[$key] ?? 0,
                'status_name' => $value
            ];
        }
        return $result;
    }


    //获取详情
    public function getOverView($id)
    {
        $plan = $this->model::query()->with([
            'planUser',
            'planCase'
        ])->find($id);
        $plan = $plan ? $plan->toArray() : [];
        //用户名称
        if ($plan) {
            //用户信息
            $plan['plan_users'] = array_unique(array_column($plan['plan_user'], 'user_id'));
            $testUsers = make(UserService::class)->getUserName($plan['plan_users']);
            $plan['test_user_name'] = implode('、', array_values($testUsers));
            $createUser = UserModel::query()->find($plan['created_by']);
            $plan['create_user_name'] = $createUser ? $createUser['lastname'] . $createUser['firstname'] : '';
            //用例数量
            $plan['case_num'] = count($plan['plan_case']);
            $caseStatus = array_count_values(array_column($plan['plan_case'], 'test_status'));
            $notStartCount = $caseStatus[TestPlanCode::CASE_STATUS_NOT_START] ?? 0;
            //已测用例数
            $plan['test_num'] = $plan['case_num'] - $notStartCount;
            //通过数
            $plan['pass_num'] = $caseStatus[TestPlanCode::CASE_STATUS_PASS] ?? 0;
            //通过率
            $plan['pass_rate'] = calculateRate($plan['pass_num'], $plan['case_num'], 2);

            // 版本名称(兼容产品模式)
            $projectExt = ProjectsExtModel::query()->where('project_id', $plan['project_id'])->first();
            $isProduct = $projectExt && $projectExt->project_type === 'product_type';
            if ($isProduct) {
                // 产品模式：从产品资料版本表获取
                $version = FilesDocProductVersionModel::query()->find($plan['version_id']);
                $plan['version_name'] = $version['version_name'] ?? '';
            } else {
                // 项目模式：从项目版本表获取（保持原有逻辑）
                $plan['version_name'] = VersionModel::query()->find($plan['version_id'])['name'] ?? '';
            }

            //进度条
            $plan['test_progress'] = $this->getTestPlanProgress(array_column($plan['plan_case'], 'test_status'));
            //周期文本
            $plan['begin_date_text'] = $plan['begin_date'] ? date('Y.m.d', strtotime($plan['begin_date'])) : '';
            $plan['end_date_text'] = $plan['end_date'] ? date('Y.m.d', strtotime($plan['end_date'])) : '';
        }

        return $plan;
    }

    /**
     * 新增/编辑
     * @param int $id
     * @param array $values
     * @return bool|Builder|Model|int
     */
    public function doEdit(int $id, array $values)
    {
        $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
        $db->beginTransaction();
        try {
            if ($id > 0) {
                $row = $this->model::query()->find($id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }
                //用例修改
                $planCase = $values['plan_case'];
                $oldPlanCaseID = TestPlanCaseModel::query()->where('test_plan_id', $id)->pluck('test_case_id')->toArray();

                $planCaseID = array_column($planCase, 'id');
                $delPlanCaseId = array_diff($oldPlanCaseID, $planCaseID);
                $addPlanCaseId = array_diff($planCaseID, $oldPlanCaseID);
                if (!empty($delPlanCaseId)) {
                    TestPlanCaseModel::query()->where('test_plan_id', $id)->whereIn('test_case_id', $delPlanCaseId)->delete();
                }
                foreach ($planCase as $item) {
                    if (in_array($item['id'], $addPlanCaseId)) {
                        $planCaseResult = TestPlanCaseModel::query()->create([
                            'test_plan_id'  => $id,
                            'test_case_id'  => $item['id'],
                            'assigned_user' => !empty($item['assigned_user']) ? $item['assigned_user'] : 0,
                        ]);
                        if (!empty($item['assigned_user'])) {
                            TestPlanLogModel::query()->create([
                                'test_plan_id' => $id,
                                'relation_id'  => $planCaseResult->id,
                                'created_by'   => getRedmineUserId(),
                                'change_type'  => 'test_plan_case',
                                'change_field' => 'assigned_user',
                                'old_value'    => 0,
                                'new_value'    => $item['assigned_user'],
                            ]);
                        }
                    } else {
                        $planCaseInfo = TestPlanCaseModel::query()->where('test_plan_id', $id)->where('test_case_id', $item['id'])->first();
                        $newAssignedUser = !empty($item['assigned_user']) ? $item['assigned_user'] : 0;
                        if ($planCaseInfo && $planCaseInfo->assigned_user != $newAssignedUser) {
                            $planCaseInfo->update(['assigned_user' => $newAssignedUser]);
                            TestPlanLogModel::query()->create([
                                'test_plan_id' => $id,
                                'relation_id'  => $planCaseInfo->id,
                                'created_by'   => getRedmineUserId(),
                                'change_type'  => 'test_plan_case',
                                'change_field' => 'assigned_user',
                                'old_value'    => $planCaseInfo->assigned_user,
                                'new_value'    => $values['assigned_user'],
                            ]);
                        }
                        TestPlanCaseModel::query()->where('test_plan_id', $id)->where('test_case_id', $item['id'])->update([
                            'assigned_user' => !empty($item['assigned_user']) ? $item['assigned_user'] : 0,
                        ]);
                    }
                }

                //用户修改
                //获取用例的用户
                $caseUser = TestPlanCaseModel::query()->where('test_plan_id', $id)->pluck('assigned_user')->toArray();
                $newPlanUser = array_unique(array_filter(array_merge($values['plan_users'], $caseUser)));
                //原来的计划用户
                $planUser = TestPlanUserModel::query()->where('test_plan_id', $id)->pluck('user_id')->toArray();
                //删除计划用户
                $delPlanUser = array_diff($planUser, $newPlanUser);
                if (!empty($delPlanUser)) {
                    TestPlanUserModel::query()->where('test_plan_id', $id)->whereIn('user_id', $delPlanUser)->delete();
                }
                //新增计划用户
                $addPlanUser = array_diff($newPlanUser, $planUser);
                if (!empty($addPlanUser)) {
                    foreach ($addPlanUser as $user) {
                        TestPlanUserModel::query()->create([
                            'test_plan_id' => $id,
                            'user_id' => $user
                        ]);
                    }
                }

                $result = $row->update($values);

                //修改测试计划状态
                make(TestPlanCaseService::class)->updatePlanStatus($id);
            } else {
                $result = $this->model::query()->create($values);
                if (!empty($result->id)) {
                    $planId = $result->id;
                    //保存用例信息
                    $case_user = [];
                    if (!empty($values['plan_case'])) {
                        foreach ($values['plan_case'] as $case) {

                            $insertPlanCase = [
                                'test_plan_id'  => $planId,
                                'test_case_id'  => $case['id'],
                                'assigned_user' => $case['assigned_user'] ?? 0,
                                'created_at'    => date('Y-m-d H:i:s')
                            ];
                            $planCase = TestPlanCaseModel::query()->create($insertPlanCase);
                            if (!empty($case['assigned_user'])) {
                                $case_user[] = $case['assigned_user'];
                                TestPlanLogModel::query()->create([
                                    'test_plan_id' => $planId,
                                    'relation_id'  => $planCase->id,
                                    'created_by'   => getRedmineUserId(),
                                    'change_type'  => 'test_plan_case',
                                    'change_field' => 'assigned_user',
                                    'old_value'    => 0,
                                    'new_value'    => $case['assigned_user'],
                                ]);
                            }
                        }
                    }
                    //保存测试用户信息
                    if (empty($values['plan_users'])) {
                        $values['plan_users'] = [];
                    } else {
                        if (!is_array($values['plan_users'])) {
                            $values['plan_users'] = explode(',', $values['plan_users']);
                        }
                    }
                    $values['plan_users'] = array_unique(array_filter(array_merge($values['plan_users'], $case_user)));
                    $insertPlanUser = [];
                    foreach ($values['plan_users'] as $user_id) {
                        $insertPlanUser[] = [
                            'test_plan_id' => $planId,
                            'user_id'      => $user_id,
                            'created_at'   => date('Y-m-d H:i:s')
                        ];
                    }
                    $insertPlanUser && TestPlanUserModel::query()->insert($insertPlanUser);
                }
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
        return $result;
    }


    /**
     * 获取版本计划列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return mixed
     */
    // public function getVersionList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    // {
    //     list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 999, VersionModel::query());
    //     $field = [
    //         'versions.*',
    //         'test_plan.version_id',
    //         Db::raw("sum(if(test_plan.status = 3,1,0)) as finish_total"),
    //         //完成总数
    //         Db::raw("sum(if(test_plan.status = 1,1,0)) as todo_total"),
    //         //待办总数
    //         Db::raw("sum(if(test_plan.status = 2,1,0)) as doing_total"),
    //         //进行中总数
    //         Db::raw("min(begin_date) as begin_date"),
    //         //开始时间
    //         Db::raw("max(end_date) as end_date"),
    //         //结束时间
    //     ];

    //     $result = $query->with('ext')
    //         ->join('test_plan', function ($join) {
    //             $join->on('test_plan.version_id', '=', 'versions.id')
    //                 ->whereNull('test_plan.deleted_at');
    //         })
    //         ->select($field)
    //         ->groupBy('version_id')->orderBy($sort, $order)->get()->toArray();
    //     foreach ($result as &$item) {
    //         $item['total'] = $item['finish_total'] + $item['todo_total'] + $item['doing_total'];
    //         if ($item['todo_total'] == $item['total']) {
    //             $item['total_status'] = 1;//未开始
    //         } elseif ($item['finish_total'] == $item['total']) {
    //             $item['total_status'] = 3;//已完成
    //         } else {
    //             $item['total_status'] = 2;//进行中
    //         }
    //         //日期格式化
    //         $item['test_plan_start_time_text'] = $item['test_plan_start_time'] ? date('m.d', strtotime($item['test_plan_start_time'])) : '';
    //         $item['test_plan_end_time_text'] =  $item['test_plan_end_time'] ? date('m.d', strtotime($item['test_plan_end_time'])) : '';
    //         $item['begin_date_text'] = $item['begin_date'] ? date('m.d', strtotime($item['begin_date'])) : '';
    //         $item['end_date_text'] = $item['end_date'] ? date('m.d', strtotime($item['end_date'])) : '';
    //         $item['finish_rate'] = calculateRate($item['finish_total'], $item['total'], 1, false);
    //     }
    //     return $result;
    // }

    /**
     * 获取版本计划列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return mixed
     */
    public function getVersionList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        $projectId = $filter['versions.project_id'] ?? 0;
        
        // ✅ 判断项目类型
        $projectExt = ProjectsExtModel::query()->where('project_id', $projectId)->first();
        $isProduct = $projectExt && $projectExt->project_type === 'product_type';
        
        if ($isProduct) {
            // 产品模式：查询产品资料版本
            return $this->getProductVersionList($projectId, $filter, $op, $sort, $order);
        } else {
            // 项目模式：查询项目版本（保持原有逻辑）
            return $this->getProjectVersionList($filter, $op, $sort, $order);
        }
    }

    /**
     * 获取产品资料版本列表
     */
    private function getProductVersionList($productId, $filter, $op, $sort, $order)
    {
        $isArchived = $filter['test_plan.is_archived'] ?? 0;
        
        $query = FilesDocProductVersionModel::query()->where('product_id', $productId);
        
        $versions = $query->orderBy($sort, $order)->get();
        
        $result = [];
        foreach ($versions as $version) {
            // 统计该版本下的测试计划数量和状态
            $planQuery = TestPlanModel::query()
                ->where('project_id', $productId)
                ->where('version_id', $version->id);
                
            if ($isArchived !== null) {
                $planQuery->where('is_archived', $isArchived);
            }
            
            $planStats = $planQuery->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as finish_total,
                SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as todo_total,
                SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as doing_total,
                MIN(begin_date) as begin_date,
                MAX(end_date) as end_date
            ')->first();
            
            $item = [
                'id' => $version->id,
                'name' => $version->version_name,
                'version_name' => $version->version_name,
                'project_id' => $productId,
                'total' => $planStats->total ?? 0,
                'finish_total' => $planStats->finish_total ?? 0,
                'todo_total' => $planStats->todo_total ?? 0,
                'doing_total' => $planStats->doing_total ?? 0,
                'begin_date' => $planStats->begin_date,
                'end_date' => $planStats->end_date,
            ];
            
            // 计算状态和进度
            if ($item['total'] == 0) {
                $item['total_status'] = 1;
                $item['finish_rate'] = 0;
            } elseif ($item['todo_total'] == $item['total']) {
                $item['total_status'] = 1; // 未开始
                $item['finish_rate'] = 0;
            } elseif ($item['finish_total'] == $item['total']) {
                $item['total_status'] = 3; // 已完成
                $item['finish_rate'] = 100;
            } else {
                $item['total_status'] = 2; // 进行中
                $item['finish_rate'] = calculateRate($item['finish_total'], $item['total'], 1, false);
            }
            
            // 日期格式化
            $item['begin_date_text'] = $item['begin_date'] ? date('m.d', strtotime($item['begin_date'])) : '';
            $item['end_date_text'] = $item['end_date'] ? date('m.d', strtotime($item['end_date'])) : '';
            $item['test_plan_start_time_text'] = $item['begin_date_text'];
            $item['test_plan_end_time_text'] = $item['end_date_text'];
            
            $result[] = $item;
        }
        
        return $result;
    }

    /**
     * 获取项目版本列表（保持原有逻辑）
     */
    private function getProjectVersionList($filter, $op, $sort, $order)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 999, VersionModel::query());
        $field = [
            'versions.*',
            'test_plan.version_id',
            Db::raw("sum(if(test_plan.status = 3,1,0)) as finish_total"),
            Db::raw("sum(if(test_plan.status = 1,1,0)) as todo_total"),
            Db::raw("sum(if(test_plan.status = 2,1,0)) as doing_total"),
            Db::raw("min(begin_date) as begin_date"),
            Db::raw("max(end_date) as end_date"),
        ];

        $result = $query->with('ext')
            ->join('test_plan', function ($join) {
                $join->on('test_plan.version_id', '=', 'versions.id')
                    ->whereNull('test_plan.deleted_at');
            })
            ->select($field)
            ->groupBy('version_id')->orderBy($sort, $order)->get()->toArray();
            
        foreach ($result as &$item) {
            $item['total'] = $item['finish_total'] + $item['todo_total'] + $item['doing_total'];
            if ($item['todo_total'] == $item['total']) {
                $item['total_status'] = 1;//未开始
            } elseif ($item['finish_total'] == $item['total']) {
                $item['total_status'] = 3;//已完成
            } else {
                $item['total_status'] = 2;//进行中
            }
            //日期格式化
            $item['test_plan_start_time_text'] = $item['test_plan_start_time'] ? date('m.d', strtotime($item['test_plan_start_time'])) : '';
            $item['test_plan_end_time_text'] =  $item['test_plan_end_time'] ? date('m.d', strtotime($item['test_plan_end_time'])) : '';
            $item['begin_date_text'] = $item['begin_date'] ? date('m.d', strtotime($item['begin_date'])) : '';
            $item['end_date_text'] = $item['end_date'] ? date('m.d', strtotime($item['end_date'])) : '';
            $item['finish_rate'] = calculateRate($item['finish_total'], $item['total'], 1, false);
            
            // ✅ 添加兼容字段
            $item['version_name'] = $item['name'];
        }
        return $result;
    }


    //获取没设置版本的数量
    public function getNoVersionCount(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 999);
        return $query->count();
    }

    //删除计划
    public function doDelete($ids): int
    {
        $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
        $db->beginTransaction();
        try {
            $ids = !is_array($ids) ? explode(',', $ids) : $ids;
            $planList = $this->model::query()->whereIn('id', $ids)->get();
            foreach ($planList as $plan) {
                TestPlanUserModel::query()->where('test_plan_id', $plan->id)->delete();
                $planCaseId = TestPlanCaseModel::query()->where('test_plan_id', $plan->id)->pluck('id')->toArray();
                $planCaseId && make(TestPlanCaseService::class)->doDelete($planCaseId);
            }
            $result = $this->model::destroy($ids);
            $db->commit();
            return $result;
        } catch (Exception $e) {
            $db->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * 测试计划相关配置项
     * @return array
     */
    public function conf()
    {
        return [
            'plan_status'      => associativelyIndex(TestPlanCode::STATUS_MAP),
            'plan_case_status' => associativelyIndex(TestPlanCode::CASE_STATUS_MAP),
        ];
    }

    //获取项目用例的树结构
    public function getProjectCaseTree($projectId)
    {
        $field = [
            'id',
            'library_id',
            'directory_id',
            'title as name',
            'priority'
        ];
        $caseList = TestCaseModel::query()->where('project_id', $projectId ?: 0)->select($field)->get()->toArray();
        $libraries = TestCaseLibraryModel::query()->where('project_id', $projectId ?: 0)
            ->select('name', 'id')->orderBy('id', 'desc')->get()->toArray();
        $directories = TestDirectoryModel::query()->where('project_id', $projectId ?: 0)
            ->select('title as name', 'id', 'parent_id', 'library_id')->get()->toArray();
        //转换目录成树状结构
        $tree = [];
        foreach ($libraries as $library) {
            $tempDir = array_filter($directories, function ($item) use ($library) {
                return $item['library_id'] == $library['id'];
            });
            $tempList = array_filter($caseList, function ($item) use ($library) {
                return $item['library_id'] == $library['id'];
            });

            $library['type'] = 'library';
            $library['key'] = 'library_' . $library['id'];
            $library['children'] = make(Tree::class)->getTreeListV5($tempDir, $tempList, true);

            //计算目录下文件数量
            $library['count'] = array_reduce($library['children'], function ($count, $val) {
                return $count + ($val['type'] === 'list' ? 1 : 0) + $val['count'];
            }, 0);
            if (!empty($library['children'])) {
                $tree[] = $library;
            }
        }
        return $tree;
    }

    /**
     * 添加用例
     * @param $planId
     * @param $params
     * @return true
     */
    public function addCase($planId, $params)
    {
        if (empty($planId) || empty($params['plan_case'])) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
        }
        $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
        $db->beginTransaction();
        try {
            foreach ($params['plan_case'] as $case) {
                TestPlanCaseModel::query()->whereNull('deleted_at')->updateOrInsert([
                    'test_plan_id' => $planId,
                    'test_case_id' => $case['id'],
                ], ['assigned_user' => empty($case['assigned_user']) ? 0 : $case['assigned_user']]);
            }
            //修改测试计划状态
            make(TestPlanCaseService::class)->updatePlanStatus($planId);
            $db->commit();
            return true;
        } catch (Exception $e) {
            $db->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    //获取用例的执行结果记录
    public function getTestResult(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        $field = [
            'test_plan_case_result.*',
            'test_plan.title',
            Db::raw('concat(users.lastname,users.firstname) as created_by_name'),
        ];
        $query = $query->join('test_plan_case', function ($join) {
            $join->on('test_plan_case.test_plan_id', '=', 'test_plan.id')
                ->whereNull('test_plan_case.deleted_at');
        })->join('test_plan_case_result', function ($join) {
            $join->on('test_plan_case_result.test_plan_case_id', '=', 'test_plan_case.id')
                ->whereNull('test_plan_case_result.deleted_at');
        })->join('users', 'users.id', '=', 'test_plan_case_result.created_by');
        $paginate = $query->select($field)
            ->orderBy($sort, $order)->paginate($limit)->toArray();
        return $paginate;
    }

}