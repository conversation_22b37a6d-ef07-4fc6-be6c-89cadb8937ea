<?php

namespace App\Core\Services\TestPlan;

use App\Constants\TestPlanCode;
use App\Core\Services\BusinessService;
use App\Core\Services\Redmine\UserService;
use App\Core\Services\Project\IssueService;
use App\Core\Services\Project\IssueStatusesService;
use App\Model\Redmine\TestPlanModel;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Utils\Parallel;

class TestPlanReportService extends BusinessService
{
    /**
     * @Inject()
     * @var TestPlanModel
     */
    protected $model;

    //获取执行结果
    private function getTestPlanReportExecResult($planTestStatus)
    {
        $statusCount = array_count_values($planTestStatus);
        $statusArr = TestPlanCode::CASE_STATUS_MAP;
        $result = [];
        foreach ($statusArr as $key => $value) {
            $result[] = [
                'status'      => $key,
                'num'         => $statusCount[$key] ?? 0,
                'status_name' => $value
            ];
        }
        return $result;
    }

    /**
     * 统计缺陷优先级数据
     */
    private function getIssuePriorityChartData($issues)
    {
        $priorityCount = [
            'P0' => 0,
            'P1' => 0,
            'P2' => 0,
            'P3' => 0,
            '待定' => 0
        ];
        
        foreach ($issues as $issue) {
            $priority = $issue['priority_text'] ?? '待定';
            if (isset($priorityCount[$priority])) {
                $priorityCount[$priority]++;
            } else {
                $priorityCount['待定']++;
            }
        }
        
        // 返回简化的数据结构，不包含样式信息
        return [
            [
                'value' => $priorityCount['P0'],
                'name' => 'P0'
            ],
            [
                'value' => $priorityCount['P1'],
                'name' => 'P1'
            ],
            [
                'value' => $priorityCount['P2'],
                'name' => 'P2'
            ],
            [
                'value' => $priorityCount['P3'],
                'name' => 'P3'
            ],
            [
                'value' => $priorityCount['待定'],
                'name' => '待定'
            ]
        ];
    }

    /**
     * 统计缺陷分布-按处理人
     */
    private function getIssueByExecutor($issues)
    {
        $executorCount = [];
        
        // 遍历每个缺陷
        foreach ($issues as $issue) {
            // 检查是否存在issue_assigned
            if (!empty($issue['issue_assigned'])) {
                // 遍历每个处理人
                foreach ($issue['issue_assigned'] as $assigned) {
                    $username = $assigned['username'] ?? '';
                    if ($username) {
                        if (!isset($executorCount[$username])) {
                            $executorCount[$username] = 1;
                        } else {
                            $executorCount[$username]++;
                        }
                    }
                }
            }
        }
        
        // 转换为所需格式
        $result = [];
        foreach ($executorCount as $name => $count) {
            $result[] = [
                'name' => $name,
                'value' => $count
            ];
        }
        
        // 按数量降序排序
        usort($result, function($a, $b) {
            return $b['value'] - $a['value'];
        });
        
        return $result;
    }

    /**
     * 缺陷分布-按处理状态
     */
    private function getIssueByStatus($issues)
    {
        // 获取所有状态
        $allStatus = make(IssueStatusesService::class)->getIssueStatus();
        
        // 创建状态映射
        $statusMap = [];
        foreach ($allStatus as $status) {
            $statusMap[$status['id']] = $status;
        }
        
        // 统计每个状态的缺陷数量
        $statusCount = [];
        
        foreach ($issues as $issue) {
            if (!empty($issue['status_list'])) {
                foreach ($issue['status_list'] as $status) {
                    if (isset($status['select']) && $status['select'] === true) {
                        $statusId = $status['id'];
                        if (!isset($statusCount[$statusId])) {
                            $statusCount[$statusId] = [
                                'id' => $statusId,  // 添加id字段
                                'name' => $status['name'],
                                'value' => 0
                            ];
                        }
                        $statusCount[$statusId]['value']++;
                        break;
                    }
                }
            }
        }
        
        // 转换为数组并按position排序
        $result = array_values($statusCount);
        usort($result, function($a, $b) use ($statusMap) {
            $positionA = $statusMap[$a['id']]['position'] ?? 999;
            $positionB = $statusMap[$b['id']]['position'] ?? 999;
            return $positionA - $positionB;
        });
        
        return $result;
    }
    

    /**
     * 获取并处理测试计划相关的缺陷数据
     */
    protected function getIssueData($planId)
    {
        // 在主流程中同步获取缺陷数据
        $issueService = make(IssueService::class);
        $filter = [
            'plan_id' => $planId,
            'tracker_id' => 20,
            'is_tree_like' => false
        ];
        $op = [
            'subject' => 'LIKE',
            'parent_id' => 'IS NULL'
        ];
        $issues = $issueService->getList($filter, $op, 'id', 'DESC', 9999);

        unset($issues['current_page']);
        unset($issues['first_page_url']);
        unset($issues['from']);
        unset($issues['last_page']);
        unset($issues['last_page_url']);
        unset($issues['next_page_url']);
        unset($issues['path']);
        unset($issues['per_page']);
        unset($issues['prev_page_url']);
        unset($issues['to']);
        unset($issues['total']);
        
        // 循环删除data中的author_text
        foreach ($issues['data'] as $key => $value) {
            unset($issues['data'][$key]['author_text']);
            unset($issues['data'][$key]['version_text']);
        }

        $issues['priority_chart'] = $this->getIssuePriorityChartData($issues['data']);
        $issues['issue_by_executor'] = $this->getIssueByExecutor($issues['data']);
        $issues['issue_by_status'] = $this->getIssueByStatus($issues['data']);

        unset($issues['data']);

        return $issues;
    }

    /**
     * 获取测试报告 - 优化版本
     */
    public function getReport($id)
    {
        // 获取测试计划数据
        $plan = $this->model::query()->with([
            'planUser',
            'planCase'
        ])->find($id);
        
        if (!$plan) {
            return [];
        }
        
        $plan = $plan->toArray();
        
        try {
            // 提取所有用户ID
            $userIds = array_unique(array_filter(array_column($plan['plan_case'], 'assigned_user')));
            $userIds[] = $plan['created_by'];
            $userIds = array_unique(array_filter($userIds));
            
            // 获取用户数据
            $userData = make(UserService::class)->getUserName($userIds);
            
            // 测试状态统计
            $testStatus = array_column($plan['plan_case'], 'test_status');
            $caseStatus = array_count_values($testStatus);
            $caseNum = count($plan['plan_case']);
            $notStartCount = $caseStatus[TestPlanCode::CASE_STATUS_NOT_START] ?? 0;
            $passNum = $caseStatus[TestPlanCode::CASE_STATUS_PASS] ?? 0;
            
            $statsData = [
                'case_num' => $caseNum,
                'test_num' => $caseNum - $notStartCount,
                'pass_num' => $passNum,
                'pass_rate' => calculateRate($passNum, $caseNum, 2),
                'test_progress' => $this->getTestPlanReportExecResult($testStatus)
            ];
            
            // 日期格式化
            $dateData = [
                'begin_date_text' => $plan['begin_date'] ? date('Y-m-d', strtotime($plan['begin_date'])) : '',
                'end_date_text' => $plan['end_date'] ? date('Y-m-d', strtotime($plan['end_date'])) : ''
            ];
            
            // 处理测试用例和用户统计
            $userStats = [];
            $processedCases = [];
            
            // 只有在测试用例数量较大时才使用并行处理
            if (count($plan['plan_case']) > 1000) {
                // 分块处理大量测试用例
                $casesParallel = new Parallel(10);
                $caseChunks = array_chunk($plan['plan_case'], max(1, ceil(count($plan['plan_case']) / 10)));
                
                foreach ($caseChunks as $index => $chunk) {
                    $casesParallel->add(function() use ($chunk, $userData) {
                        $processedCases = [];
                        $chunkUserStats = [];
                        
                        foreach ($chunk as $case) {
                            $userId = $case['assigned_user'];
                            $userName = $userData[$userId] ?? '';
                            $case['assigned_user_name'] = $userName;
                            
                            $processedCases[] = $case;
                            
                            if ($userId && !isset($chunkUserStats[$userId])) {
                                $chunkUserStats[$userId] = [
                                    'name' => $userName,
                                    'value' => 0
                                ];
                            }
                            
                            if ($userId) {
                                $chunkUserStats[$userId]['value']++;
                            }
                        }
                        
                        return [
                            'cases' => $processedCases,
                            'stats' => $chunkUserStats
                        ];
                    }, 'chunk_' . $index);
                }
                
                // 等待所有测试用例处理完成
                $chunkResults = $casesParallel->wait();
                
                // 处理结果
                foreach ($chunkResults as $result) {
                    $processedCases = array_merge($processedCases, $result['cases']);
                    
                    // 合并用户统计
                    foreach ($result['stats'] as $userId => $stat) {
                        if (!isset($userStats[$userId])) {
                            $userStats[$userId] = $stat;
                        } else {
                            $userStats[$userId]['value'] += $stat['value'];
                        }
                    }
                }
            } else {
                // 对于小量数据，直接同步处理
                foreach ($plan['plan_case'] as $case) {
                    $userId = $case['assigned_user'];
                    $userName = $userData[$userId] ?? '';
                    $case['assigned_user_name'] = $userName;
                    
                    $processedCases[] = $case;
                    
                    if ($userId && !isset($userStats[$userId])) {
                        $userStats[$userId] = [
                            'name' => $userName,
                            'value' => 0
                        ];
                    }
                    
                    if ($userId) {
                        $userStats[$userId]['value']++;
                    }
                }
            }
            
            // 按任务数量排序用户统计
            usort($userStats, function($a, $b) {
                return $b['value'] - $a['value'];
            });
            
            // 更新测试计划数据
            $plan['plan_case'] = $processedCases;
            $plan['case_executor'] = array_values($userStats);
            
            // 合并其他数据
            $plan = array_merge($plan, $statsData, $dateData);
            
            // 获取缺陷数据
            $issues = $this->getIssueData($id);
            $plan['issues'] = $issues;

            unset($plan['plan_case']);
            unset($plan['plan_user']);
            return $plan;
            
        } catch (\Throwable $e) {

            // 返回基本数据
            return [
                'id' => $id,
                'error' => '生成报告时发生错误: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 保存测试结论
     */
    public function saveTestConclusion($id, $conclusion)
    {
        $this->model::query()->where('id', $id)->update(['conclusion' => $conclusion]);
        return true;
    }
}