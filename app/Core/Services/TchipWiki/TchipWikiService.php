<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2025/04/02 16:49
     * <AUTHOR>
     * @Description
     */

    namespace App\Core\Services\TchipWiki;

    use App\Constants\BiWikiCode;
    use App\Constants\StatusCode;
    use App\Core\Services\AuthService;
    use App\Event\Wiki\WikiDocumentCreatedEvent;
    use App\Event\Wiki\WikiDocumentDeletedEvent;
    use App\Event\Wiki\WikiDocumentPinnedEvent;
    use App\Event\Wiki\WikiDocumentLikedEvent;
    use App\Event\Wiki\WikiDocumentViewedEvent;
    use App\Event\Wiki\WikiDocumentViewMilestoneEvent;
    use Psr\EventDispatcher\EventDispatcherInterface;
    use App\Exception\AppException;
    use App\Model\Redmine\WikiContentModel;
    use App\Model\TchipBi\UserModel;
    use App\Model\TchipBi\WikiCatalogMemberModel;
    use App\Model\TchipBi\WikiCommentModel;
    use App\Model\TchipBi\WikiDocumentModel;
    use App\Model\TchipBi\WikiDocumentStatisticModel;
    use App\Model\TchipBi\WikiDocumentTagModel;
    use App\Model\TchipBi\WikiDocumentVersionModel;
    use App\Model\TchipBi\WikiLikeModel;
    use App\Model\TchipBi\WikiReplyModel;
    use App\Model\TchipBi\WikiSpaceMemberModel;
    use App\Model\TchipBi\WikiSpaceModel;
    use App\Model\TchipBi\WikiUserGroupMappingModel;
    use App\Model\TchipBi\WikiUserGroupModel;
    use App\Model\TchipBi\WikiCatalogModel;
    use Carbon\Carbon;
    use Exception;
    use Hyperf\Database\Model\Builder;
    use Hyperf\Database\Model\Model;
    use Hyperf\DbConnection\Db;
    use Hyperf\Di\Annotation\Inject;
    use Hyperf\Logger\LoggerFactory;
    use Hyperf\Redis\Redis;
    use Qbhy\HyperfAuth\AuthManager;
    use Hyperf\HttpServer\Annotation\AutoController;
    use App\Core\Services\TchipWiki\WikiExportService;

    class TchipWikiService extends \App\Core\Services\BusinessService
    {


        /**
         * @Inject
         * @var LoggerFactory
         */
        protected LoggerFactory $loggerFactory;

        /**
         * @Inject()
         * @var AuthManager
         */
        protected AuthManager $auth;


        /**
         * @Inject
         * @var WikiLikeModel
         */
        protected WikiLikeModel $wikiLikeModel;

        /**
         * @Inject
         * @var WikiSpaceModel
         */
        protected WikiSpaceModel $wikiSpaceModel;

        /**
         * @Inject
         * @var WikiReplyModel
         */
        protected WikiReplyModel $wikiReplyModel;

        /**
         * @Inject
         * @var WikiCommentModel
         */
        protected WikiCommentModel $wikiCommentModel;

        
        /**
         * @Inject
         * @var WikiCatalogModel
         */
        protected WikiCatalogModel $wikiCatalogModel;

        /**
         * @Inject
         * @var WikiDocumentModel
         */
        protected WikiDocumentModel $wikiDocumentModel;

        /**
         * @Inject
         * @var WikiUserGroupModel
         */
        protected WikiUserGroupModel $wikiUserGroupModel;

        /**
         * @Inject
         * @var WikiSpaceMemberModel
         */
        protected WikiSpaceMemberModel $wikiSpaceMemberModel;

        /**
         * @Inject
         * @var WikiCatalogMemberModel
         */
        protected WikiCatalogMemberModel $wikiCatalogMemberModel;

        /**
         * @Inject
         * @var WikiDocumentTagModel
         */
        protected WikiDocumentTagModel $wikiDocumentTagModel;

        /**
         * @Inject
         * @var WikiDocumentVersionModel
         */
        protected WikiDocumentVersionModel $wikiDocumentVersionModel;

        /**
         * @Inject
         * @var WikiUserGroupMappingModel
         */
        protected WikiUserGroupMappingModel $wikiUserGroupMappingModel;

        /**
         * @Inject
         * @var WikiDocumentStatisticModel
         */
        protected WikiDocumentStatisticModel $wikiDocumentStatisticModel;

        /**
         * @Inject
         * @var WikiExportService
         */
        protected WikiExportService $wikiExportService;

        /**
         * @Inject
         * @var EventDispatcherInterface
         */
        protected EventDispatcherInterface $eventDispatcher;

        //////////////////////////////////////////////
        /// Wiki目录
        /**
         * Wiki目录-获取列表(支持树形结构和文档关联)
         * @param array $filter 筛选条件
         * @param array $op 操作符
         * @param string $sort 排序字段
         * @param string $order 排序方式
         * @param int $limit 每页数量
         * @param int $page 页码
         * @return array
         */
        public function getWikiCatalogList(array $filter = [], array $op = [], string $sort = 'catalog_id', string $order = 'DESC', int $limit = 10, int $page = 1)
        {
            $filterDocUnion = false; // 是否混杂文档一同筛选
            if (!empty($filter['catalog_id']) && $filter['catalog_id'] == -3) {
                unset($filter['catalog_id']);
                $filterDocUnion = true;
            }

            // is_public是否公开（20250508 pm 讨论暂定为 0默认组内共享|1公开共享非组员也可见|-1仅创建者私有可见）
            $userId = $this->auth->user()->getId();
            $userRole = $this->getUserRoleBySpaceId($filter['space_id'], $userId);
            // 判断是否超级管理员
            $authService = make(AuthService::class);
            $isSuper = $authService->isSuper($userId, ['Director']);
             if ($isSuper) {
                 $userRole = 'owner';
             }
             // 超管全部获取
            if (!empty($userRole) || $isSuper) {
                $filter['is_public'] = $isSuper ? '0,1,-1' : '0,1';
                $op['is_public'] = 'in';
            } else {
                $filter['is_public'] = '1';
                $op['is_public'] = '=';
            }


            /** @var $query WikiCatalogModel */
            $query = $this->wikiCatalogModel::query();
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            
             // 成员可以看到自己创建的私有目录
            $query->orWhere(function($subq) use ($userId) {
                $subq->where('is_public', -1)
                    ->where('created_by', $userId);
            });


            // 使用方式1: 获取对应目录id下的文档与子目录用以可分页列表展示
            if (isset($filter['pid']) && $filter['pid'] >= 0) {
                $isGetCatalogData = empty($filter['catalog_id']);
                
                // 目录部分的数据获取
                if ($isGetCatalogData) {
                    $query = $query->select([
                        'catalog_id as node_id', 
                        'space_id', 
                        'pid as parent_id', 
                        'name as title', 
                        'path', 
                        'created_at', 
                        'created_by', 
                        'sort_order',
                        DB::raw("NULL as view_count"), // 添加缺失的字段
                        DB::raw("NULL as like_count"), // 添加缺失的字段
                        DB::raw("0 as is_pinned"), // 目录不支持置顶
                        DB::raw("NULL as pinned_at"), // 目录不支持置顶
                        DB::raw("'catalog' as type")
                    ]);
                }
                
                // 文档部分的数据获取
                /* @var $query2 WikiDocumentModel */
                $query2 = $this->wikiDocumentModel::query();
                list($query2, $limit, $sort, $order) = $this->buildparams(['catalog_id' => $filter['pid'], 'space_id' => $filter['space_id']], [], $sort, $order, $limit, $query2);
                
                // 如果有 title，手动加入 title 的查询条件
                if (!empty($filter['title'])) {
                    $query2 = $query2->where('title', 'like', "%{$filter['title']}%");
                }
                
                // 如果有 created_by，手动加入 created_by 的查询条件
                if (!empty($filter['created_by'])) {
                    $query2 = $query2->where('created_by', $filter['created_by']);
                }
                

                // 文档权限过滤（与目录权限一致）
                if (!$isSuper) {
                    $query2->where(function($subQuery) use ($userId, $userRole) {
                        $subQuery->where('is_public', 1)  // 公开文档
                            ->orWhere(function($q) use ($userRole) {
                                if (!empty($userRole)) {
                                    $q->where('is_public', 0);  // 组内共享文档，需要是空间成员
                                }
                            })
                            ->orWhere(function($q) use ($userId) {
                                $q->where('is_public', -1)  // 私有文档，只有创建者可见
                                  ->where('created_by', $userId);
                            });
                    });
                }
                
                $query2 = $query2->select([
                    'doc_id as node_id', 
                    'space_id', 
                    'catalog_id as parent_id', 
                    'title', 
                    'path', 
                    'created_at', 
                    'created_by',
                    'sort_order',
                    'view_count', 
                    'like_count',
                    'is_pinned',
                    'pinned_at',
                    DB::raw("'document' as type")
                ]);
                
                // 执行查询
                if ($isGetCatalogData) {
                    // 合并目录和文档查询
                    $result = $query->unionAll($query2);
                } else {
                    // 仅文档查询
                    $result = $query2;
                }
                
                // 分页结果
                $paginate = $result
                    ->orderBy('type')  // 目录在前 (catalog < document)
                    ->orderBy('sort_order', 'desc')
                    ->paginate($limit);
                
                // 将 $paginate 转换为数组
                $paginate = $paginate ? $paginate->toArray() : [];
                
                // 获取创建者信息（包含用户等级信息）
                $authorIds = array_column($paginate['data'], 'created_by');
                $authorIds = array_unique($authorIds);
                $authors = UserModel::with('userPoints:user_id,level,level_name,current_points')
                    ->whereIn('id', $authorIds)
                    ->get()
                    ->keyBy('id');
                $authors = $authors ? $authors->toArray() : [];
                
                // 提取所有目录的ID
                $catalogIds = [];
                foreach ($paginate['data'] as $item) {
                    if ($item['type'] === 'catalog') {
                        // 使用 node_id 而不是 catalog_id，因为在查询中我们将 catalog_id 重命名为 node_id
                        $catalogIds[] = $item['node_id'];
                    }
                }
                
                // 获取所有目录的子孙目录ID映射
                $catalogIdMap = [];
                foreach ($catalogIds as $catalogId) {
                    $catalogIdMap[$catalogId] = $this->getAllChildCatalogIds($catalogId); 
                }
                
                // 统计所有目录及其子孙目录的 doc_count
                $docCounts = [];
                if (!empty($catalogIdMap) && !empty(array_merge(...array_values($catalogIdMap)))) {
                    $docCounts = $this->wikiDocumentModel::whereIn('catalog_id', array_merge(...array_values($catalogIdMap)))
                        ->select('catalog_id', DB::raw('COUNT(*) as count'))
                        ->groupBy('catalog_id')
                        ->get()
                        ->pluck('count', 'catalog_id')
                        ->toArray();
                }
                
                // 合并各目录及其子孙目录的 doc_count
                $totalDocCounts = [];
                foreach ($catalogIdMap as $parentId => $childIds) {
                    if (!empty($childIds)) {
                        $totalDocCounts[$parentId] = array_sum(array_map(function ($id) use ($docCounts) {
                            return $docCounts[$id] ?? 0;
                        }, $childIds));
                    } else {
                        $totalDocCounts[$parentId] = 0;
                    }
                }
                
                foreach ($paginate['data'] as &$item) {
                    $item['author_text'] = $item['created_by'] ? ($authors[$item['created_by']] ?? null) : null;
                    
                    // 统计目录下文档数量
                    if ($item['type'] === 'catalog') {
                        // 使用 node_id 而不是 catalog_id
                        $item['doc_count'] = $totalDocCounts[$item['node_id']] ?? 0;
                    }
                }

                return $paginate;
            }
            
            // 使用方式2: 未去筛选具体的目录，获取所有目录下文档拼接到对应目录下（整体展现 无分页）
            $paginate = $query
                ->orderBy('sort_order', 'desc')
                ->orderBy($sort, $order)
                ->paginate(9999999);
            $paginate = $paginate ? $paginate->toArray() : [];
            $paginate['data'] = array_map(function ($item) {
                $item['node_id'] = $item['catalog_id'];
                $item['title'] = $item['name'];
                return $item;
            }, $paginate['data']);


            if ($filterDocUnion) {
                $catalogIdArray = array_column($paginate['data'], 'catalog_id');
                array_unshift($catalogIdArray, "0"); // 添加 "0" 到最前面
                $catalogIds = implode(',', $catalogIdArray);
                $documents = $this->getList(['catalog_id' => $catalogIds, 'space_id' => $filter['space_id']], ['catalog_id' => 'IN'], 'doc_id', 'ASC', 99999, false);
                //$documents = $documents->toArray() ?? [];
                
                // 使用 array_map 为每个元素添加 pid 字段
                $documents['data'] = array_map(function ($doc) {
                    $doc['pid'] = $doc['catalog_id']; // 将 catalog_id 的值赋给 pid
                    $doc['node_id'] = $doc['doc_id'];
                    $doc['node_id'] = $doc['node_id'] * -1; // 防止参与树形结构构造时出现ID冲突
                    return $doc;
                }, $documents['data']);
                
                // 后追加$documents['data']，保证目录皆在文档上方
                $paginate['data'] = array_merge($paginate['data'], $documents['data']);
            }

//            // 过滤孤立节点并将其删除 20250717递归删除情形下不需要这项防备删除
//            $orphans = $this->filterOrphans($paginate['data'], 'pid');
//            if (!empty($orphans)) {
//                $this->doWikiCatalogDelete($orphans);
//            }
            
            $paginate['flattenedData'] = $paginate['data'];

            $paginate['data'] = $this->getTreeListV2_1($paginate['data'], 0, 'pid', 'catalog_id');
            return $paginate;
        }

        /**
         * 构建树形结构，并保持目录在文档上方的排序
         * 
         * @param array $hashMap 节点哈希表
         * @param int $parentId 父节点ID
         * @param int $tree_depth 树深度
         * @return array 排序后的树形结构
         */
        private function buildTree(array $hashMap, int $parentId, int $tree_depth): array
        {
            $treeList = [];
            if (!isset($hashMap[$parentId])) {
                return $treeList; // 没有子节点
            }

            // 先按节点类型分组
            $catalogs = [];
            $documents = [];
            
            foreach ($hashMap[$parentId] as $item) {
                // 判断是目录还是文档 (node_id < 0 表示文档)
                if (isset($item['node_id']) && $item['node_id'] < 0) {
                    $documents[] = $item;
                } else {
                    $catalogs[] = $item;
                }
            }
            
            // 分别对目录和文档按 sort_order 排序
            usort($catalogs, function($a, $b) {
                return $b['sort_order'] <=> $a['sort_order']; // 降序排列
            });
            
            usort($documents, function($a, $b) {
                return $b['sort_order'] <=> $a['sort_order']; // 降序排列
            });
            
            // 先处理所有目录
            foreach ($catalogs as $item) {
                $item['tree_depth'] = $tree_depth;
                
                // 使用 node_id 作为子节点的父ID
                $nodeId = $item['node_id'] ?? 0;
                
                // 防止死循环：检查是否尝试构建自己的子树
                if ($nodeId !== $parentId) {
                    $children = $this->buildTree($hashMap, $nodeId, $tree_depth + 1);
                    
                    // 计算当前节点及其子节点中的负 id 数量
                    $negativeIdCount = $this->calculateNegativeIds($item, $children);
                    
                    if (!empty($children)) {
                        $item['children'] = $children;
                    }
                    
                    // 将负 id 总数记录在当前节点中
                    $item['doc_count'] = $negativeIdCount;
                } else {
                    // 如果发现循环引用，设置空子节点
                    $item['doc_count'] = 0;
                }
                
                $treeList[] = $item;
            }
            
            // 再处理所有文档
            foreach ($documents as $item) {
                $item['tree_depth'] = $tree_depth;
                $item['doc_count'] = 1; // 文档本身计数为1
                $treeList[] = $item;
            }

            return $treeList;
        }

        /**
         * 重新改造getTreeList方法，确保目录在文档上方
         */
        public function getTreeListV2_1(array $data, int $parentId = 0, string $parentName = 'pid', string $idName = 'catalog_id', $order = 'DESC', $tree_depth = 0): array
        {
            // 构建父子关系哈希表
            $hashMap = [];
            foreach ($data as $item) {
                $hashMap[$item[$parentName]][] = $item;
            }
            
            // 递归生成树形结构，确保目录在文档上方
            return $this->buildTree($hashMap, $parentId, $tree_depth);
        }

        public function calculateNegativeIds(array $item, array $children): int
        {
            // 当前节点是否为负 id
            $negativeIdCount = $item['node_id'] < 0 ? 1 : 0;

            // 累加子节点中的负 id 数量
            foreach ($children as $child) {
                $negativeIdCount += $child['doc_count'] ?? 0;
            }

            return $negativeIdCount;
        }

        /**
         * 获取所有子目录ID（包括自身）
         * @param int $parentId 父目录ID
         * @return array 目录ID数组
         */
        protected function getAllChildCatalogIds($parentId)
        {
            // 先获取父目录的路径
            $parentCatalog = $this->wikiCatalogModel::query()->find($parentId);
                        if (!$parentCatalog) {
                return [$parentId]; // 如果找不到父目录，只返回父ID本身
            }
            
                    $parentPath = $parentCatalog->path;
            
            // 使用LIKE查询获取所有子目录（路径中包含父目录路径的都是子目录）
            $childCatalogs = $this->wikiCatalogModel::query()
                ->where('path', 'like', $parentPath . '%')
                ->pluck('catalog_id')
                ->toArray();
            
            // 确保父ID本身也包含在结果中
            if (!in_array($parentId, $childCatalogs)) {
                $childCatalogs[] = $parentId;
            }
            
            return $childCatalogs;
        }

        /**
         * 获取文档列表
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */
        public function getDocumentList(array $filter = [], array $op = [], string $sort = 'doc_id', string $order = 'DESC', int $limit = 10)
        {
            $query = $this->wikiDocumentModel::query();
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            
            // 使用综合权限过滤方法，支持跨空间权限校验
            $userId = $this->auth->user()->getId();
            $this->addDocumentPermissionFilter($query, $userId);
            
            return $query->orderBy($sort, $order)->paginate($limit);
        }

        /**
         * 过滤并获取孤立的节点ID
         * @param array $data
         * @param string $parentName
         * @return array
         */
        private function filterOrphans(array $data, string $parentName = 'pid'): array
        {
            $allIds = array_column($data, 'catalog_id');
            $orphanIds = [];
            
            foreach ($data as $item) {
                if (!empty($item[$parentName]) && $item[$parentName] > 0 && !in_array($item[$parentName], $allIds)) {
                    $orphanIds[] = $item['catalog_id'];
                }
            }
            
            return $orphanIds;
        }

        /**
         * Wiki目录-新增/编辑
         * @param int $catalog_id
         * @param array $values
         * @return bool|Builder|Model|int
         */
        public function doWikiCatalogEdit(int $catalog_id, array $values)
        {
            if ($catalog_id > 0) {
                $row = $this->wikiCatalogModel::query()->find($catalog_id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }

                // 如果更改了父目录，需要更新path
                if (isset($values['pid']) && $values['pid'] != $row->pid) {
                    $values['path'] = $this->buildCatalogPath($values['pid'], $catalog_id);
                }

                $row->update($values);
                return $row;
            } else {
                $values['created_by'] = $this->auth->user()->getId();

                // 创建时先不设置path，创建后再更新
                $values['path'] = '/';
                $result = $this->wikiCatalogModel::query()->create($values);

                // 创建后更新正确的path
                $correctPath = $this->buildCatalogPath($values['pid'] ?? 0, $result->catalog_id);
                $result->path = $correctPath;
                $result->save();

                return $result;
            }
        }

        /**
         * 构建目录路径
         * @param int $parentId 父目录ID
         * @param int $catalogId 当前目录ID
         * @return string
         */
        private function buildCatalogPath(int $parentId, int $catalogId): string
        {
            if ($parentId == 0) {
                // 根目录
                return '/' . $catalogId . '/';
            }

            $parentCatalog = $this->wikiCatalogModel::query()->find($parentId);
            if (!$parentCatalog) {
                throw new AppException(StatusCode::ERR_SERVER, __('父目录不存在'));
            }

            // 父目录路径 + 当前目录ID + /
            return $parentCatalog->path . $catalogId . '/';
        }



        /**
         * Wiki目录-删除（包括子目录）
         * @param $ids 要删除的目录ID
         * @return int 成功返回1
         */
        public function doWikiCatalogDelete($ids): int
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);

                // 获取要删除的目录信息
                $catalogs = $this->wikiCatalogModel::query()
                    ->whereIn('catalog_id', $ids)
                    ->get(['catalog_id', 'path'])
                    ->toArray();

                if (empty($catalogs)) {
                    return 0; // 没有找到目录，无需删除
                }

                // 收集所有要删除的目录ID（包括子目录）
                $allCatalogIds = $ids;
                foreach ($catalogs as $catalog) {
                    // 查找以当前目录path开头的所有子目录
                    $childCatalogs = $this->wikiCatalogModel::query()
                        ->where('path', 'like', $catalog['path'] . '%')
                        ->where('catalog_id', '!=', $catalog['catalog_id']) // 排除自身
                        ->pluck('catalog_id')
                        ->toArray();

                    // 将子目录ID加入到要删除的列表中
                    $allCatalogIds = array_merge($allCatalogIds, $childCatalogs);
                }

                // 确保ID列表唯一
                $allCatalogIds = array_unique($allCatalogIds);

                // 查询并删除这些目录下的所有文档
                $this->wikiDocumentModel::query()->whereIn('catalog_id', $allCatalogIds)->delete();

                // 删除目录（包括子目录）
                $this->wikiCatalogModel::query()->whereIn('catalog_id', $allCatalogIds)->delete();

                $this->loggerFactory->get('wiki_catalog_delete')->info('wiki目录删除', [
                    'user_id' => $this->auth->user()->getId(),
                    'catalog_ids' => $allCatalogIds
                ]);

                DB::commit();
                return 1;
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * Wiki目录-重新排序
         *
         * 用于对Wiki目录节点进行重新排序操作，包括拖动到平级位置或成为其他节点的子级。
         *
         * @param int|string $dragNodeId   拖动的节点 ID。
         * @param array $targetNode        目标节点信息（包含节点 ID 和其他必要属性）。
         * @param bool $dropToGap          是否拖动到平级位置：
         *                                 - true：拖动到平级。
         *                                 - false：拖动成为子级。
         * @param int $dropPosition        拖动位置的相对位置标志：
         *                                 - 1：拖动到目标节点的下方，平级。
         *                                 - 0：拖动成为目标节点的子级。
         *                                 - -1：拖动到目标节点的上方，平级。
         *
         * @throws Exception               如果拖动操作或排序操作失败，将抛出异常。
         * @return int                     成功返回1
         */
        public function wikiCatalogRearrangement($dragNodeId, $targetNode, $dropToGap, $dropPosition)
        {
            // 打印拖拽详细信息
            //var_dump(['拖拽节点ID' => $dragNodeId, '目标节点' => $targetNode, '是否平级拖拽' => $dropToGap, '拖拽位置' => $dropPosition, '拖拽类型' => ($dragNodeId < 0 ? '文档' : '目录').'拖至'.($targetNode['node_id'] < 0 ? '文档' : '目录')]);
            
            // 判断拖动节点和目标节点的类型
            $dragNodeIsDoc = $dragNodeId < 0; // 文档id传进时为负数
            $targetNodeIsDoc = $targetNode['node_id'] < 0;
            
            // 如果是目录拖动到文档的任何位置，都抛出异常
            if (!$dragNodeIsDoc && $targetNodeIsDoc) {
                throw new AppException(StatusCode::ERR_SERVER, '目录不能拖动到文档位置');
            }
            
            DB::beginTransaction();
            try {
                $dragNodeId = abs($dragNodeId);
                $targetNodeId = abs($targetNode['node_id']);
                $targetCatalogId = $targetNodeIsDoc ? $targetNode['catalog_id'] : $targetNode['node_id'];

                ////////////////////////////////////////////////
                // 1.文档之间的移动 || 文档移动到目录之下 
                ////////////////////////////////////////////////
                if ($dragNodeIsDoc) {
                    $dragNode = $this->wikiDocumentModel::query()->where('doc_id', $dragNodeId)->first()->toArray();
                    // 判断是否跨目录
                    $isCrossCatalog = ($dragNode['catalog_id'] != $targetCatalogId);

                    if (!$targetNodeIsDoc && $isCrossCatalog && $dropPosition != 0 && $dropToGap) {
                        // 并非要成为子级而是拖动到其下
                        $targetCatalogId = $targetNode['pid'];
                        $isCrossCatalog = $targetNode['pid'] != $dragNode['catalog_id']; // 需要再次判断是否跨目录
                    }

                    $allDocsAtSameCatalog = $this->getList(['catalog_id' => $targetCatalogId, 
                        'space_id' => $targetNode['space_id']],
                        ['catalog_id' => 'IN'], 'sort_order', 'desc', 99999);
                    $allDocs = $allDocsAtSameCatalog['data'];

                    // 找到目标节点的位置下标
                    $targetIndex = 0;
                    if ($targetNodeIsDoc) {
                        foreach ($allDocs as $index => $doc) {
                            if ($doc['doc_id'] == $targetNodeId) {
                                $targetIndex = $index;
                                break;
                            }
                        }
                        if ($targetIndex === null) {
                            throw new AppException(StatusCode::ERR_SERVER, '目标节点未找到');
                        }
                    }


                    // 两种情况
                    if ($isCrossCatalog) {
                        // 跨目录：在目标节点之后插入拖拽节点dragNode
                        array_splice($allDocs, $targetNodeIsDoc ? $targetIndex + 1 : 0, 0, [['doc_id' => $dragNodeId, 'sort_order' => 0]]);
                        if ($targetCatalogId <= 0) {
                            throw new AppException(StatusCode::ERR_SERVER, '不允许文档脱离目录单独存在');
                        }
                        // 跨目录的处理 需要跨目录的修改记录
                        $this->doEdit($dragNodeId, ['catalog_id' => $targetCatalogId], false);
                    } else {
                        // 非跨目录：先删除原位置节点，再在目标位置插入
                        $dragIndex = null;
                        foreach ($allDocs as $index => $doc) {
                            if ($doc['doc_id'] == $dragNodeId) {
                                $dragIndex = $index;
                                break;
                            }
                        }
                        if ($dragIndex === null) {
                            throw new AppException(StatusCode::ERR_SERVER, '拖拽节点未找到');
                        }
                        // 先删除原位置
                        $dragNode = $allDocs[$dragIndex];
                        array_splice($allDocs, $dragIndex, 1);
                        // 在目标位置之后插入 目标位置在拖拽节点初始位置之后的情况($targetIndex < $dragIndex ? 1 : 0 )
                        array_splice($allDocs, $targetNodeIsDoc ? $targetIndex + ($targetIndex < $dragIndex ? 1 : 0 ) : 0, 0, [$dragNode]);
                    }

                    $totalDocs = count($allDocs);
                    // 重新定义所有文档的 sort_order
                    foreach ($allDocs as $index => $doc) {
                        $newSortOrder = $totalDocs - $index; // 根据访问顺序反向写入 sort_order 因为使用的是desc排序规则
                        DB::table('wiki_documents')
                            ->where('doc_id', $doc['doc_id'])
                            ->update([
                                'sort_order' => $newSortOrder,
                                'updated_at' => date('Y-m-d H:i:s'),
                            ]);
                    }
                }

                ////////////////////////////////////////////////
                // 2.目录拖动 - 只处理目录到目录的拖动
                ////////////////////////////////////////////////
                if (!$dragNodeIsDoc) {
                    $dragCatalogNode = $this->wikiCatalogModel::query()->where('catalog_id', $dragNodeId)->first()->toArray();
                    // 判断是否跨目录操作
                    $isSameParentCatalog = ($dragCatalogNode['pid'] == $targetNode['pid']);
                    // 目录是否跨父目录，或者拖动到目标节点的子节点
                    $isCrossCatalogForDir = !$isSameParentCatalog || !$dropToGap;

                    $allCatalogsAtSameDirectory = $this->getWikiCatalogList([
                        'pid' => $dropToGap ? $targetNode['pid'] : $targetCatalogId, // $dropToGap true成为同级，否则成为子级
                        'space_id' => $dragCatalogNode['space_id'],
                        'catalog_id' => -3,
                        ],
                        [], 'sort_order', 'desc', 99999);

                    // 剔除 文档 元素
                    $allCatalogs = array_filter($allCatalogsAtSameDirectory['data'], function ($catalog) {
                        return $catalog['type'] == 'catalog';
                    });

                    // 找到目标节点的位置下标
                    $targetCatalogIndex = -1; // 需要设置为-1，处理跨目录的情况
                    if (!$targetNodeIsDoc) {
                        foreach ($allCatalogs as $index => $catalog) {
                            if ($catalog['node_id'] == $targetNodeId) {
                                $targetCatalogIndex = $index;
                                break;
                            }
                        }
                        if ($targetCatalogIndex === null && $dropToGap) {
                            throw new AppException(StatusCode::ERR_SERVER, '目标节点未找到');
                        }
                    }

                    // 是否为跨目录
                    if ($isCrossCatalogForDir) {
                        // 跨目录：在目标节点之后插入拖拽节点dragNode
                        array_splice($allCatalogs, !$targetNodeIsDoc && $dropPosition !== -1 ?
                            $targetCatalogIndex + 1
                            : 0, 0, [['node_id' => $dragNodeId, 'sort_order' => 0]]);
                        // 跨目录的处理 需要跨目录的修改记录
                        $this->doWikiCatalogEdit($dragNodeId, ['pid' => !$dropToGap ? $targetCatalogId : $targetNode['pid']]);
                    } else {
                        // 非跨目录：先删除原位置节点，再在目标位置插入
                        $dragIndex = null;
                        foreach ($allCatalogs as $index => $catalog) {
                            if ($catalog['node_id'] == $dragNodeId) {
                                $dragIndex = $index;
                                break;
                            }
                        }
                        if ($dragIndex === null) {
                            throw new AppException(StatusCode::ERR_SERVER, '拖拽节点未找到');
                        }
                        // 先删除原位置
                        $dragNode = $allCatalogs[$dragIndex];
                        array_splice($allCatalogs, $dragIndex, 1);
                        // 在目标位置之后插入
                        array_splice($allCatalogs, $dropPosition !== -1 ? $targetCatalogIndex + ($dragIndex > $targetCatalogIndex ? 1 : 0) : 0, 0, [$dragNode]);
                    }
                    $totalCatalogCount = count($allCatalogs);
                    // 构造批量更新的参数 重新定义所有目录的 sort_order
                    $updateData = [];
                    foreach ($allCatalogs as $index => $catalog) {
                        $newSortOrder = $totalCatalogCount - $index; // 根据访问顺序反向写入 sort_order
                        $updateData[] = [
                            'catalog_id' => $catalog['node_id'],
                            'sort_order' => $newSortOrder,
                        ];
                    }
                    if (empty($updateData)) {
                        throw new AppException(StatusCode::ERR_SERVER, '没有有效的目录需要更新');
                    }
                    // 使用 CASE 表达式单次更新所有记录，避免多次循环更新
                    $sql = "UPDATE bi_wiki_catalogs SET sort_order = CASE ";
                    foreach ($updateData as $catalogItem) {
                        $sql .= "WHEN catalog_id = {$catalogItem['catalog_id']} THEN {$catalogItem['sort_order']} ";
                    }
                    $sql .= "END, updated_at = '" . date('Y-m-d H:i:s') . "' ";
                    // 添加 WHERE 子句
                    $ids = array_column($updateData, 'catalog_id');
                    $sql .= "WHERE catalog_id IN (" . implode(',', $ids) . ")";
                    DB::statement($sql);

                    // 更新受影响目录的路径
                    $this->updateCatalogPaths($dragNodeId);
                }
                DB::commit();
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
            return 1;
        }

        /**
         * 更新目录及其所有子目录的路径
         * 
         * @param int $catalogId 需要更新路径的目录ID
         * @return void
         */
        private function updateCatalogPaths($catalogId)
        {
            // 获取当前目录信息
            $catalog = $this->wikiCatalogModel::query()->find($catalogId);
            if (!$catalog) {
                return;
            }

            // 构建新的路径
            if ($catalog->pid == 0) {
                // 顶级目录，路径格式为 /
                $newPath = '/' . $catalog->catalog_id. '/';
            } else {
                // 非顶级目录，获取父目录路径
                $parentCatalog = $this->wikiCatalogModel::query()->find($catalog->pid);
                if ($parentCatalog) {
                    // 父目录存在，路径格式为 父目录路径 + 当前ID + /
                    // 确保父目录路径以 / 结尾
                    $parentPath = $parentCatalog->path;
                    if (substr($parentPath, -1) != '/') {
                        $parentPath .= '/';
                    }
                    $newPath = $parentPath . $catalog->catalog_id . '/';
                } else {
                    // 父目录不存在（异常情况），设置为根路径加当前ID
                    $newPath = '/' . $catalog->catalog_id . '/';
                }
            }

            // 更新当前目录的路径
            $catalog->path = $newPath;
            $catalog->save();

            // 获取所有子目录并递归更新
            $childCatalogs = $this->wikiCatalogModel::query()->where('pid', $catalogId)->get();
            foreach ($childCatalogs as $childCatalog) {
                // 递归更新所有子目录的路径
                $this->updateCatalogPaths($childCatalog->catalog_id);
            }
        }

        /// Wiki目录 END
        //////////////////////////////////////////////


        //////////////////////////////////////////////
        /// Wiki文档
        /**
         * Wiki文档-获取列表
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */
        public function getList(array $filter = [], array $op = [], string $sort = 'doc_id', string $order = 'DESC', int $limit = 10, $is_pinned = true)
        {
            $userId = $this->auth->user()->getId();

            // 当手动传入sort，取消置顶的加入
            if (!empty($filter['custom_sort'])) {
                $is_pinned = false;
                unset($filter['custom_sort']);
            }

            /** @var $query WikiDocumentModel */
            $query = $this->wikiDocumentModel::query();

            // 使用 leftJoin 方式处理状态筛选，提高查询性能
            $hasStatusFilter = isset($filter['audit_status']);

            if ($hasStatusFilter) {
                // 添加状态表的 leftJoin，使用别名避免冲突
                $query->leftJoin('wiki_document_status as audit_status', function($join) {
                    $join->on('wiki_documents.doc_id', '=', 'audit_status.doc_id')
                         ->where('audit_status.status_type', '=', 'audit');
                });

                // 选择主表字段和状态字段
                $query->select([
                    'wiki_documents.*',
                    'audit_status.status_value as audit_status_value',
                    'audit_status.created_at as audit_status_created_at',
                    // 'audit_status.expired_at as audit_status_expired_at',
                ]);
            }

            // 处理审核状态筛选
            if (isset($filter['audit_status'])) {
                $auditStatusValue = (int) $filter['audit_status'];
                if ($auditStatusValue == 1) {
                    // 审核通过
                    $query->where('audit_status.status_value', 1);
                } elseif ($auditStatusValue == 0) {
                    // 未审核（包括没有审核记录和审核状态为待审核的）
                    $query->where(function($subQuery) {
                        $subQuery->whereNull('audit_status.status_value')
                                 ->orWhere('audit_status.status_value', 0);
                    });
                } elseif ($auditStatusValue == 2) {
                    // 审核拒绝
                    $query->where('audit_status.status_value', 2);
                }
                unset($filter['audit_status']);
                unset($op['audit_status']);
            }

            // 处理标签筛选
            if (isset($filter['tag_id'])) {
                // 获取标签ID
                $tagIds = explode(',', $filter['tag_id']);
                
                // 添加标签筛选条件 - 同时检查中间表的deleted_at字段
                $query->whereHas('tags', function($subQuery) use ($tagIds) {
                    $subQuery->whereIn('tags.tag_id', $tagIds)
                            ->whereNull('wiki_document_tags.deleted_at'); // 添加这一行来检查中间表的deleted_at
                });
                
                // 从常规过滤条件中移除tag_id
                unset($filter['tag_id']);
                unset($op['tag_id']);
            }

            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);


            $query = $query->with(['space' => function ($q) use ($userId){
                $q->with(['members' => function ($q2) use ($userId) {
                    $q2->where('user_id', $userId)->orWhereIn('group_id', $this->getJoinedGroupIds($userId));
                }]);
            }]);

            $query = $query->with([
//                'updater',
//                'contentUpdater',
//                'creator',
                'creator.userPoints:user_id,level,level_name,current_points',
                'updater.userPoints:user_id,level,level_name,current_points',
                'contentUpdater.userPoints:user_id,level,level_name,current_points',
                //闭包函数，获取中间表的deleted_at不为null的标签
                'tags' => function($query) {
                    $query->whereNull('wiki_document_tags.deleted_at');
                },
                // 加载文档状态信息
                'auditStatus',
                'premiumStatus',
                'trainingStatus'
            ]);

            $query = $query->withCount([
             'replies',
                'comments',
                'like'
            ]);

            // 使用综合权限过滤方法，支持跨空间权限校验
            $this->addDocumentPermissionFilter($query, $userId);

            // 添加置顶排序：置顶文档优先显示，按置顶时间倒序，然后按原排序条件
            $result = $query
                ->when($is_pinned, function ($query) {
                    $query->orderBy('is_pinned', 'DESC')
                        ->orderBy('pinned_at', 'DESC');
                })
                ->orderBy($sort, $order)
                ->paginate($limit)->toArray();

            // 提取当前用户角色
            // 定义角色优先级（数值越大权限越高）
            $rolePriority = BiWikiCode::ROLE_PRIORITY_EN;
            foreach ($result['data'] as &$item) {
                $currentRole = null;
                $maxPriority = -1;
                if (!empty($item['space']['members']) && is_array($item['space']['members'])) {
                    foreach ($item['space']['members'] as $member) {
                        $role = $member['role'] ?? null;
                        if ($role && ($rolePriority[$role] ?? 0) > $maxPriority) {
                            $currentRole = $role;
                            $maxPriority = $rolePriority[$role];
                        }
                    }
                }
                $item['current_user_role'] = $currentRole;
                $item['comments_count'] = ($item['comments_count'] + $item['replies_count'])?? 0;
                
                // 添加文档状态标签
                $item['status_tags'] = [];
                
                // 检查审核状态
                if (isset($item['audit_status_value']) && $item['audit_status_value'] == 1) {
                    $item['status_tags'][] = [
                        'type' => 'audit',
                        'label' => '已审核',
                        'color' => 'success'
                    ];
                }

                // 检查精华认证
                if (isset($item['premium_status_value']) && $item['premium_status_value'] == 1) {
                    $item['status_tags'][] = [
                        'type' => 'premium',
                        'label' => '精华',
                        'color' => 'warning'
                    ];
                }

                // 检查培训认证
                if (isset($item['training_status_value']) && $item['training_status_value'] == 1) {
                    $item['status_tags'][] = [
                        'type' => 'training',
                        'label' => '培训',
                        'color' => 'info'
                    ];
                }
            }


            return $result;
        }

        public function setChangeLog($doc_id, $values)
        {
            $latestVersion = $this->wikiDocumentVersionModel::query()
                ->where('doc_id', $doc_id)
                ->orderBy('version_number', 'desc')
                ->first();

            // 内容无变更时，不新增版本记录
            if (
                $latestVersion &&
                $latestVersion->content_html === ($values['content_html'] ?? '') &&
                $latestVersion->content_markdown === ($values['content_markdown'] ?? '') &&
                $latestVersion->editor_type == ($values['editor_type'] ?? 1)
            ) {
                return;
            }

            $newVersionNumber = $latestVersion ? $latestVersion->version_number + 1 : 1;

            $this->wikiDocumentVersionModel::query()->create([
                'doc_id' => $doc_id,
                'version_number' => $newVersionNumber,
                'content_html' => $values['content_html'] ?? '',
                'content_markdown' => $values['content_markdown'] ?? '',
                'editor_type' => $values['current_editor_type'] ?? 1,
                'created_by' => $this->auth->user()->getId(),
                'change_log' => $values['change_log'] ?? '',
            ]);
        }



        /**
         * Wiki文档-新增/编辑
         * @param int $id
         * @param array $values
         * @return bool|Builder|Model|int
         */
        public function doEdit(int $doc_id, array $values, $needLog = true)
        {
            Db::beginTransaction();
            try {
                // 定义内容相关字段
                $contentFields = ['title', 'content_html', 'content_markdown', 'current_editor_type'];
                
                // 检查是否有内容修改
                $hasContentChange = array_intersect_key($values, array_flip($contentFields));
                
                if ($doc_id > 0) {
                    // 更新文档
                    $row = $this->wikiDocumentModel::query()->find($doc_id);
                    if (!$row) {
                        throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                    }

                    $userId = $this->auth->user()->getId();
                    
                    // 检查文档编辑权限
                    $this->checkDocumentEditPermission($row, $userId);
                    $values['updated_by'] = !empty($values['updated_by']) ? $values['updated_by'] : $userId;
                    
                    // 如果有内容修改，更新内容相关的时间戳和用户
                    if ($hasContentChange) {
                        $values['content_updated_by'] = $userId;
                        $values['content_updated_at'] = Carbon::now();
                    }
                    
                    $result = $row->update($values);
                    $needLog && $this->setChangeLog($doc_id, $values);
                } else {
                    // 创建新文档
                    $userId = $this->auth->user()->getId();
                    $values['created_by'] = !empty($values['created_by']) ? $values['created_by'] : $userId;
                    $values['updated_by'] = $values['created_by'];
                    $values['content_updated_by'] = $values['created_by'];
                    $values['content_updated_at'] = Carbon::now();
                    
                    // 如果没有指定文档权限，从目录继承权限
                    if (!isset($values['is_public']) && !empty($values['catalog_id'])) {
                        $catalog = $this->wikiCatalogModel::query()->find($values['catalog_id']);
                        if ($catalog) {
                            $values['is_public'] = $catalog->is_public;
                        }
                    }
                    
                    $result = $this->wikiDocumentModel::query()->create($values);

                    // 传入了指定的创建日期更新日期，需要替换默认生成的日期
                    if (!empty($values['created_at']) || !empty($values['updated_at'])) {
                        $result->created_at = $values['created_at'] ?? $result->created_at;
                        $result->updated_at = $values['updated_at'] ?? $result->updated_at;
                        $result->save();
                    }

                    // path字段路径维护
                    $parentId = $values['parent_id'] ?? null;
                    $parentDoc = $parentId ? $this->wikiDocumentModel::query()->find($parentId) : null;
                    $nowDoc = $this->wikiDocumentModel::query()->find($result->doc_id);
                    $nowDoc->path = $nowDoc->doc_id;
                    if ($parentDoc && $nowDoc) {
                        $nowDoc->path = $parentDoc->path . '-' . $nowDoc->doc_id;
                        $nowDoc->save();
                    }

                    // 触发文档创建事件
                    $this->eventDispatcher->dispatch(
                        new WikiDocumentCreatedEvent(
                            $result->doc_id,
                            $values['created_by'],
                            $values['title'] ?? '',
                            $values
                        )
                    );
                }
                
                Db::commit();
            } catch (AppException $e) {
                Db::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }

            return $result;
        }

        /**
         * Wiki文档-批量新增/编辑
         * @param array $documents 文档数据数组
         * @return array 批量操作结果
         */
        public function doBatchEdit(array $documents)
        {
            if (empty($documents)) {
                throw new AppException(StatusCode::ERR_SERVER, __('文档数据不能为空'));
            }

            $results = [];
            $successCount = 0;
            $failureCount = 0;
            $errors = [];

            foreach ($documents as $index => $document) {
                try {
                    $docId = (int) ($document['doc_id'] ?? 0);
                    unset($document['doc_id']);
                    
                    // 直接复用现有的doEdit方法，每个文档都有自己的事务
                    $result = $this->doEdit($docId, $document, false);
                    
                    $results[] = [
                        'index' => $index,
                        'doc_id' => $result->doc_id ?? $result->id ?? $docId,
                        'status' => 'success',
                        'data' => $result
                    ];
                    $successCount++;
                    
                } catch (\Exception $e) {
                    $results[] = [
                        'index' => $index,
                        'doc_id' => $docId ?? null,
                        'status' => 'error',
                        'message' => $e->getMessage()
                    ];
                    $errors[] = "文档索引 {$index}: " . $e->getMessage();
                    $failureCount++;
                }
            }

            return [
                'total' => count($documents),
                'success_count' => $successCount,
                'failure_count' => $failureCount,
                'results' => $results,
                'errors' => $errors
            ];
        }

        /**
         * Wiki文档-删除
         * @param $ids
         * @return int
         */
        public function doDelete($ids): int
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);

                // 获取要删除的文档信息，用于扣除积分
                $documents = $this->wikiDocumentModel::query()
                    ->whereIn('doc_id', $ids)
                    ->get(['doc_id', 'title', 'created_by']);

                foreach ($documents as $document) {
                    // 触发文档删除事件
                    $this->eventDispatcher->dispatch(
                        new WikiDocumentDeletedEvent(
                            $document->doc_id,
                            $document->created_by,
                            $this->auth->user()->getId(),
                            $document->title
                        )
                    );
                }

                $this->wikiDocumentModel::query()->whereIn('doc_id', $ids)->delete();

                DB::commit();
                return 1;
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }


        /**
         * Wiki文档批量置顶
         * @param array $ids
         * @param string $pinned_reason
         * @return array
         */
        public function doWikiDocumentBatchPin(array $ids, string $pinned_reason = ''): array
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);
                $user_id = $this->auth->user()->getId();
                $success_count = 0;
                $fail_count = 0;

                foreach ($ids as $doc_id) {
                    try {
                        $document = $this->wikiDocumentModel::query()->find($doc_id);
                        if (!$document) {
                            $fail_count++;
                            $this->loggerFactory->get('wiki')->warning('批量置顶失败：文档不存在', [
                                'doc_id' => $doc_id,
                                'user_id' => $user_id,
                                'reason' => 'document_not_found'
                            ]);
                            continue;
                        }

                        // 检查是否已经置顶
                        if ($document->is_pinned) {
                            $fail_count++;
                            $this->loggerFactory->get('wiki')->info('批量置顶失败：文档已经置顶', [
                                'doc_id' => $doc_id,
                                'title' => $document->title,
                                'user_id' => $user_id,
                                'reason' => 'already_pinned'
                            ]);
                            continue;
                        }

                        // // 检查权限
                        // if (!$this->checkSpacePermission($document->space_id, $user_id, 'manage')) {
                        //     $fail_count++;
                        //     $this->loggerFactory->get('wiki')->warning('批量置顶失败：权限不足', [
                        //         'doc_id' => $doc_id,
                        //         'title' => $document->title,
                        //         'user_id' => $user_id,
                        //         'space_id' => $document->space_id,
                        //         'reason' => 'permission_denied'
                        //     ]);
                        //     continue;
                        // }

                        // 更新置顶状态
                        $now = Carbon::now();
                        $document->update([
                            'is_pinned' => 1,
                            'pinned_at' => $now,
                            'pinned_by' => $user_id,
                            'pinned_reason' => $pinned_reason,
                            'pinned_revoke_deadline' => $now->copy()->addDay() // 设置1天后的撤回截止时间
                        ]);

                        // 触发文档置顶事件
                        $this->eventDispatcher->dispatch(
                            new WikiDocumentPinnedEvent(
                                $document->doc_id,
                                $document->created_by,
                                $user_id,
                                $document->title,
                                true, // 置顶
                                [
                                    'ever_pinned' => $document->ever_pinned,
                                    'ever_pinned_at' => $document->ever_pinned_at,
                                ]
                            )
                        );

                        $success_count++;
                        
                        $this->loggerFactory->get('wiki')->info('文档置顶成功', [
                            'doc_id' => $document->doc_id,
                            'title' => $document->title,
                            'user_id' => $user_id,
                            'pinned_reason' => $pinned_reason
                        ]);
                    } catch (Exception $e) {
                        $fail_count++;
                        $this->loggerFactory->get('wiki')->error('批量置顶异常', [
                            'doc_id' => $doc_id,
                            'user_id' => $user_id,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                }

                DB::commit();
                return [
                    'message' => "批量置顶完成，成功：{$success_count}个，失败：{$fail_count}个",
                    'success_count' => $success_count,
                    'fail_count' => $fail_count
                ];
            } catch (AppException $e) {
                DB::rollBack();
                throw $e;
            } catch (Exception $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * Wiki文档批量取消置顶
         * @param array $ids
         * @return array
         */
        public function doWikiDocumentBatchUnpin(array $ids): array
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);
                $user_id = $this->auth->user()->getId();
                $success_count = 0;
                $fail_count = 0;

                foreach ($ids as $doc_id) {
                    try {
                        $document = $this->wikiDocumentModel::query()->find($doc_id);
                        if (!$document) {
                            $fail_count++;
                            $this->loggerFactory->get('wiki')->warning('批量取消置顶失败：文档不存在', [
                                'doc_id' => $doc_id,
                                'user_id' => $user_id,
                                'reason' => 'document_not_found'
                            ]);
                            continue;
                        }

                        // 检查是否已经取消置顶
                        if (!$document->is_pinned) {
                            $fail_count++;
                            $this->loggerFactory->get('wiki')->info('批量取消置顶失败：文档已经取消置顶', [
                                'doc_id' => $doc_id,
                                'title' => $document->title,
                                'user_id' => $user_id,
                                'reason' => 'already_unpinned'
                            ]);
                            continue;
                        }

                        // // 检查权限
                        // if (!$this->checkSpacePermission($document->space_id, $user_id, 'manage')) {
                        //     $fail_count++;
                        //     $this->loggerFactory->get('wiki')->warning('批量取消置顶失败：权限不足', [
                        //         'doc_id' => $doc_id,
                        //         'title' => $document->title,
                        //         'user_id' => $user_id,
                        //         'space_id' => $document->space_id,
                        //         'reason' => 'permission_denied'
                        //     ]);
                        //     continue;
                        // }

                        // 检查是否超过1天，如果超过则记录为曾被置顶
                        $now = Carbon::now();
                        $updateData = [
                            'is_pinned' => 0,
                            'pinned_at' => null,
                            'pinned_by' => null,
                            'pinned_reason' => null,
                            'pinned_revoke_deadline' => null
                        ];

                        // 置顶时间超过1天，标记为曾被置顶
                        if ($document->pinned_revoke_deadline && $now->gt($document->pinned_revoke_deadline)) {
                            $updateData['ever_pinned'] = 1;
                            if (!$document->ever_pinned_at) {
                                $updateData['ever_pinned_at'] = $document->pinned_at;
                            }
                        }

                        $document->update($updateData);

                        // 触发文档取消置顶事件
                        $this->eventDispatcher->dispatch(
                            new WikiDocumentPinnedEvent(
                                $document->doc_id,
                                $document->created_by,
                                $user_id,
                                $document->title,
                                false, // 取消置顶
                                [
                                    'ever_pinned' => $document->ever_pinned,
                                    'ever_pinned_at' => $document->ever_pinned_at,
                                ]
                            )
                        );

                        $success_count++;
                        
                        $this->loggerFactory->get('wiki')->info('文档取消置顶成功', [
                            'doc_id' => $document->doc_id,
                            'title' => $document->title,
                            'user_id' => $user_id
                        ]);
                    } catch (Exception $e) {
                        $fail_count++;
                        $this->loggerFactory->get('wiki')->error('批量取消置顶异常', [
                            'doc_id' => $doc_id,
                            'user_id' => $user_id,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                }

                DB::commit();
                return [
                    'message' => "批量取消置顶完成，成功：{$success_count}个，失败：{$fail_count}个",
                    'success_count' => $success_count,
                    'fail_count' => $fail_count
                ];
            } catch (AppException $e) {
                DB::rollBack();
                throw $e;
            } catch (Exception $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * Wiki文档-最近更新的Wiki文档列表获取
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */
        public function getWikiRecentUpdate(array $filter = [], array $op = [], string $sort = 'updated_at', string $order = 'DESC', int $limit = 10)
        {
            // 如果传入user_id，则查询该用户有权限查看的文档
            $user_id = $filter['user_id'] ?? null;
            unset($filter['user_id']);

            /** @var $query WikiDocumentModel */
            $query = $this->wikiDocumentModel::query();
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            
            // 添加关联查询以获取目录和空间信息（用于权限判断），包含用户等级信息
            $query = $query->with([
                'space', 
                'catalog',
                'comments', 
                'updater.userPoints:user_id,level,level_name,current_points',
                'creator.userPoints:user_id,level,level_name,current_points',
                'contentUpdater.userPoints:user_id,level,level_name,current_points'
            ])->withCount(['comments', 'like', 'replies']);

            // 使用新的综合权限过滤方法
            $this->addDocumentPermissionFilter($query, $user_id);

            $result = $query->orderBy($sort, $order)->paginate($limit)->toArray();

            // 手动将 space.space_name 提取到顶层
            $result['data'] = array_map(function ($item) {
                $item['space_name'] = $item['space']['space_name'] ?? null;
                $item['comments_count'] = ($item['comments_count'] + $item['replies_count']) ?? 0;
                return $item;
            }, $result['data']);
            $result['data'] = array_values($result['data']);
            return $result;
        }

        /**
         * Wiki文档-全局搜索
         * 
         * @param string $keyword 搜索关键词
         * @param array $filter 其他筛选条件
         * @param array $op 筛选条件运算符
         * @param string $sort 排序字段
         * @param string $order 排序方式
         * @param int $limit 每页数量
         * @return array 搜索结果
         */
        public function getWikiSearch(string $keyword, array $filter = [], array $op = [], string $sort = 'updated_at', string $order = 'DESC', int $limit = 10)
        {
            // 获取所有符合权限验证的目录id
            $accessibleCatalogIds = $this->getAccessibleCatalogIds();

            // 将关键词分词处理
            $keywords = $this->splitKeywords($keyword);

            /** @var $query WikiDocumentModel */
            $query = $this->wikiDocumentModel::query();

            // 限制只能搜索有权限的目录的文档
            if (!empty($accessibleCatalogIds)) {
                $query->whereIn('catalog_id', $accessibleCatalogIds);
            }

            // 根据关键词搜索标题或内容
            $query->where(function ($q) use ($keywords) {
                foreach ($keywords as $kw) {
                    $q->orWhere('title', 'like', "%{$kw}%")
                      ->orWhere('content_html', 'like', "%{$kw}%")
                      ->orWhere('content_markdown', 'like', "%{$kw}%");
                }
            });

            // 应用其他筛选条件
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            
            // 加载关联数据（包含用户等级信息）
            $query = $query->with([
                'space',
                'creator.userPoints:user_id,level,level_name,current_points',
                'updater.userPoints:user_id,level,level_name,current_points',
                'contentUpdater.userPoints:user_id,level,level_name,current_points'
            ])->withCount(['comments', 'like']);

            // 执行查询并返回分页结果
            $result = $query->orderBy($sort, $order)->paginate($limit)->toArray();

            // 处理搜索结果，高亮匹配的关键词
            foreach ($result['data'] as &$item) {
                // 高亮标题中的关键词
                $item['highlighted_title'] = $this->highlightKeywords($item['title'], $keywords);
                
                // 提取并高亮内容片段
                $content = $item['content_markdown'] ?: strip_tags($item['content_html']);
                $item['content_snippet'] = $this->extractContentSnippet($content, $keywords);
                
                // 补充空间和目录信息
                $item['space_name'] = $item['space']['space_name'] ?? '';
                $item['catalog_name'] = $item['catalog']['name'] ?? '';
                
                // 移除不需要返回的大字段
                unset($item['content_html']);
                unset($item['content_markdown']);
            }
            
            return $result;
        }

        /**
         * 分词处理搜索关键词
         * 
         * @param string $keyword 原始关键词
         * @return array 分词结果
         */
        private function splitKeywords(string $keyword): array
        {
            // 使用空格分割关键词
            $keywords = preg_split('/\s+/', trim($keyword));
            
            // 过滤空字符串并去重
            $keywords = array_filter(array_unique($keywords));
            
            // 如果分词后为空，则使用原始关键词
            if (empty($keywords)) {
                return [$keyword];
            }
            
            return $keywords;
        }

        /**
         * 高亮文本中的关键词
         * 
         * @param string $text 原始文本
         * @param array $keywords 关键词数组
         * @return string 高亮后的文本
         */
        private function highlightKeywords(string $text, array $keywords): string
        {
            foreach ($keywords as $keyword) {
                if (empty($keyword)) continue;
                
                $text = preg_replace(
                    '/(' . preg_quote($keyword, '/') . ')/i',
                    '<span class="highlight">$1</span>',
                    $text
                );
            }
            
            return $text;
        }

        /**
         * 提取包含关键词的内容片段
         * 
         * @param string $content 原始内容
         * @param array $keywords 关键词数组
         * @param int $snippetLength 片段长度
         * @return string 内容片段
         */
        private function extractContentSnippet(string $content, array $keywords, int $snippetLength = 200): string
        {
            // 查找第一个匹配的关键词位置（使用 mb_stripos 而非 stripos）
            $position = -1;
            foreach ($keywords as $keyword) {
                if (empty($keyword)) {
                    continue;
                }

                // 字符偏移（可正确处理中文）
                $pos = mb_stripos($content, $keyword);
                if ($pos !== false && ($position === -1 || $pos < $position)) {
                    $position = $pos;
                }
            }

            // 如果没找到，则返回内容开头
            if ($position === -1) {
                return mb_substr($content, 0, $snippetLength)
                    . (mb_strlen($content) > $snippetLength ? '...' : '');
            }

            // 计算截取起点，依然基于"字符偏移"
            $start = max(0, $position - floor($snippetLength / 2));

            $snippet = mb_substr($content, $start, $snippetLength);

            // 添加省略号
            if ($start > 0) {
                $snippet = '...' . $snippet;
            }
            if ($start + $snippetLength < mb_strlen($content)) {
                $snippet .= '...';
            }

            // 高亮关键词（高亮函数不变，但它接收的也是纯字符截取结果）
            $snippet = $this->highlightKeywords($snippet, $keywords);

            return $snippet;
        }



        /**
         * Wiki文档导出为PDF
         * 
         * @param int $doc_id 文档ID
         * @return array 包含文件路径和文件名的数组
         * @throws AppException 
         */
        public function wikiExport(int $doc_id)
        {
            // 获取当前操作用户
            $currentUser = $this->auth->user();
            $currentUserId = $currentUser->getId();
            $currentUserName = $currentUser->name ?? '未知用户';
            
            // 检查权限（非超级管理员需要检查访问权限）
            $authService = make(AuthService::class);
            $isSuper = $authService->isSuper($currentUserId, ['Director']);
            
            // 获取用户可访问的空间ID
            $accessibleSpaceIds = [];
            if (!$isSuper) {
                $joinedSpaceIds = $this->getJoinedSpaceIds($currentUserId);
                $publicSpaceIds = $this->wikiSpaceModel::query()->where('is_public', 1)->pluck('space_id')->toArray();
                $accessibleSpaceIds = array_unique(array_merge($joinedSpaceIds, $publicSpaceIds));
            }
            
            // 调用导出服务
            return $this->wikiExportService->exportToPdfStream($doc_id, $currentUserId, $currentUserName, $accessibleSpaceIds);
        }

        /**
         * 递归导出目录及其所有子目录的文档为ZIP
         * 
         * @param int $catalog_id 顶级目录ID
         * @param bool $includeCatalogStructure 是否在ZIP中保留目录结构
         * @return array 包含文件路径和文件名的数组
         * @throws AppException 
         */
        public function wikiExportCatalogRecursive(int $catalog_id, bool $includeCatalogStructure = true)
        {
            // 获取当前操作用户
            $currentUser = $this->auth->user();
            $currentUserId = $currentUser->getId();
            $currentUserName = $currentUser->name ?? '未知用户';

            // 检查权限（非超级管理员需要检查访问权限）
            $authService = make(AuthService::class);
            $isSuper = $authService->isSuper($currentUserId, ['Director']);
            
            // 获取用户可访问的空间ID
            $accessibleSpaceIds = [];
            if (!$isSuper) {
                $joinedSpaceIds = $this->getJoinedSpaceIds($currentUserId);
                $publicSpaceIds = $this->wikiSpaceModel::query()->where('is_public', 1)->pluck('space_id')->toArray();
                $accessibleSpaceIds = array_unique(array_merge($joinedSpaceIds, $publicSpaceIds));
            }

            // 获取当前目录信息，用于ZIP文件命名
            $currentCatalog = $this->wikiCatalogModel::query()->findOrFail($catalog_id);
            
            // 获取目录及其所有子目录的ID
            $allCatalogIds = $this->getAllChildCatalogIds($catalog_id);
            
            // 准备存储每个目录及其文档的结构
            $catalogStructure = [];
            
            // 获取所有目录的基本信息
            $catalogsInfo = $this->wikiCatalogModel::query()
                ->whereIn('catalog_id', $allCatalogIds)
                ->get(['catalog_id', 'name', 'pid', 'path'])
                ->keyBy('catalog_id')
                ->toArray();
            
            // 获取所有目录下的所有文档
            $allDocuments = $this->wikiDocumentModel::query()
                ->whereIn('catalog_id', $allCatalogIds)
                ->get(['doc_id', 'catalog_id', 'title'])
                ->toArray();
            
            // 将文档ID按目录分组
            $docIdsByCatalog = [];
            foreach ($allDocuments as $document) {
                $docIdsByCatalog[$document['catalog_id']][] = [
                    'doc_id' => $document['doc_id'],
                    'title' => $document['title']
                ];
            }
            

            // 所有要导出的文档ID
            $allDocIds = array_column($allDocuments, 'doc_id');
            
            if (empty($allDocIds)) {
                throw new AppException(StatusCode::ERR_SERVER, '所选目录下没有任何文档');
            }
            
            // 保存目录结构信息（用于生成ZIP时创建目录结构）
            foreach ($allCatalogIds as $cId) {
                $catalogInfo = $catalogsInfo[$cId] ?? null;
                if ($catalogInfo) {
                    $catalogStructure[$cId] = [
                        'name' => $catalogInfo['name'],
                        'pid' => $catalogInfo['pid'],
                        'path' => $catalogInfo['path'],
                        'documents' => $docIdsByCatalog[$cId] ?? []
                    ];
                }
            }
            
            // 调用导出服务，生成ZIP文件
            return $this->wikiExportService->exportToPdfWithStructureStream(
                $allDocIds,
                $currentUserId,
                $currentUserName,
                $accessibleSpaceIds,
                $catalogStructure,
                $includeCatalogStructure,
                $currentCatalog['name'] ?? '文档导出'
            );
        }

        /**
         * Wiki文档-获取所有文档
         */
        public function getAllWikiDocument(array $filter = [], array $op = [], string $sort = 'doc_id', string $order = 'DESC', int $limit = 10)
        {
            // 获取所有符合权限验证的目录id
            $accessibleCatalogIds = $this->getAccessibleCatalogIds();

            // 确保目录ID不为空，避免SQL错误
            if (empty($accessibleCatalogIds)) {
                // 如果没有可访问的目录，返回空结果
                return [];
            }
            
            // 将目录ID转为逗号分隔的字符串
            array_unshift($accessibleCatalogIds, "0"); // 添加 "0" 到最前面
            $filter['catalog_id'] = implode(',', $accessibleCatalogIds);
            $op['catalog_id'] = 'IN';

            // 获取所有符合权限验证的文档
            return $this->getList(
                $filter,
                $op,
                $sort,
                $order,
                $limit
            );
        }


        /**
         * Wiki文档-导入项目Wiki,根据项目Wiki的id,导入到当前项目
         * @param array $ids 项目wiki的id
         * @param int $catalog_id 当前项目的目录id
         * @throws AppException 
         */
        public function importProjectWiki(array $ids, int $catalog_id)
        {
            $catalog = $this->wikiCatalogModel->find($catalog_id);
            if (!$catalog) {
                throw new AppException(StatusCode::ERR_SERVER, '目录信息不存在');
            }
            $spaceId = $catalog->space_id;

            // 获取项目wiki信息
            $projectWikis = make(WikiContentModel::class)::query()
                ->whereIn('page_id', $ids)
                ->with('page')
                ->get()
                ->toArray();
            if (empty($projectWikis)) {
                throw new AppException(StatusCode::ERR_SERVER, '项目wiki信息不存在');
            }

            $redmineUserIds = array_column($projectWikis, 'author_id');
            $biUserIdsMap = redmineUserIdsToBiUserids($redmineUserIds);

            //$values = [];
            foreach ($projectWikis as $wiki) {
                $values = [
                    //'doc_id' => -1,
                    'catalog_id' => $catalog_id,
                    'space_id' => $spaceId,
                    'title' => $wiki['page']['title'],
                    'content_markdown' => $wiki['text'],
                    'content_html' => $wiki['text_html'],
                    'created_at' => $wiki['page']['created_on'],
                    'updated_at' => $wiki['updated_on'],
                    'updated_by' => $biUserIdsMap[$wiki['author_id']],
                    'created_by' => $biUserIdsMap[$wiki['author_id']],
                    'wiki_from_import' => 1,
                    'current_editor_type' => $wiki['text_html'] ? 1 : 2,
                ];
                $this->doEdit(-1, $values, false);
            }
            return 1;
        }


        /// Wiki文档 END
        //////////////////////////////////////////////


        //////////////////////////////////////////////
        /// wiki文档点赞

        /**
         * wiki文档点赞-获取列表
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */
        public function getWikiLikeList(array $filter = [], array $op = [], string $sort = 'like_id', string $order = 'DESC', int $limit = 10)
        {
            // 如果filter中包含doc_id，说明是在查看特定文档的点赞列表，此时记录文档阅读
            if (isset($filter['doc_id']) && $filter['doc_id'] > 0) {
                $this->recordDocumentView($filter['doc_id']);
            }

            $query = $this->wikiLikeModel::query();
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            $query = $query->with('user');
            return $query->orderBy($sort, $order)->paginate($limit)->toArray();
        }

        /**
         * 记录文档阅读
         * @param int $docId 文档ID
         * @return void
         */
        private function recordDocumentView(int $docId): void
        {
            try {
                $userId = $this->auth->user()->getId();
                $redis = make(Redis::class);

                // 防抖机制：同一用户对同一文档在5分钟内只计算一次阅读
                $debounceKey = "wiki:view:debounce:{$userId}:{$docId}";
                if ($redis->exists($debounceKey)) {
                    return; // 防抖期内，不重复计数
                }

                // 设置防抖标记，5分钟过期
                $redis->setex($debounceKey, 300, 1);

                // 获取文档信息
                $document = $this->wikiDocumentModel::query()->find($docId);
                if (!$document) {
                    return;
                }

                // 更新文档阅读计数
                $newViewCount = $this->wikiDocumentModel::query()
                    ->where('doc_id', $docId)
                    ->increment('view_count');

                // 获取更新后的阅读计数
                $document->refresh();
                $currentViewCount = $document->view_count;

                // // 触发文档阅读事件
                // $this->eventDispatcher->dispatch(
                //     new WikiDocumentViewedEvent(
                //         $docId,
                //         $userId,
                //         $document->created_by,
                //         $document->title,
                //         $currentViewCount
                //     )
                // );

                // 检查是否达到阅读量里程碑（每10个阅读量触发一次）
                if ($currentViewCount > 0 && $currentViewCount % 10 === 0) {
                    $this->eventDispatcher->dispatch(
                        new WikiDocumentViewMilestoneEvent(
                            $docId,
                            $document->created_by,
                            $document->title,
                            $currentViewCount,
                            $currentViewCount // 里程碑值
                        )
                    );
                }

            } catch (\Exception $e) {
                // 记录错误但不影响主流程
                $logger = make(LoggerFactory::class)->get('wiki');
                $logger->error('记录文档阅读失败', [
                    'doc_id' => $docId,
                    'user_id' => $userId ?? null,
                    'error' => $e->getMessage()
                ]);
            }
        }


        /**
         * wiki文档点赞-新增/编辑
         * @param int $like_id
         * @param array $values
         * @return bool|Builder|Model|int
         */
        public function doWikiLikeEdit(int $like_id, array $values)
        {
            if ($like_id > 0) {
                $row = $this->wikiLikeModel::query()->find($like_id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }
                $row->update($values);
                return $row;
            } else {
                $values['user_id'] = $this->auth->user()->getId();

                DB::beginTransaction();
                try {
                    $result = $this->wikiLikeModel::query()->create($values);

                    // 更新文档点赞数
                    $this->wikiDocumentModel::query()
                        ->where('doc_id', $values['doc_id'])
                        ->increment('like_count');

                    // 获取文档信息，触发点赞事件,本人点赞除外
                    $document = $this->wikiDocumentModel::query()->find($values['doc_id']);
                    if ($document && $document->created_by != $values['user_id']) {
                        $this->eventDispatcher->dispatch(
                            new WikiDocumentLikedEvent(
                                $document->doc_id,
                                $document->created_by,
                                $values['user_id'],
                                $document->title,
                                true // 点赞
                            )
                        );
                    }

                    DB::commit();
                    return $result;
                } catch (\Exception $e) {
                    DB::rollBack();
                    throw $e;
                }
            }
        }

        /**
         * wiki文档点赞-删除
         * @param $ids
         * @return int
         */
        public function doWikiLikeDelete($ids): int
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);

                // 获取要删除的点赞记录信息，用于扣除积分
                $likeRecords = $this->wikiLikeModel::query()
                    ->whereIn('like_id', $ids)
                    ->with('doc')
                    ->get();

                foreach ($likeRecords as $likeRecord) {
                    if ($likeRecord->doc) {
                        // 减少文档点赞数
                        $this->wikiDocumentModel::query()
                            ->where('doc_id', $likeRecord->doc_id)
                            ->decrement('like_count');

                        if ($likeRecord->doc->created_by != $this->auth->user()->getId()) {
                            // 触发取消点赞事件 本人点赞除外
                            $this->eventDispatcher->dispatch(
                                new WikiDocumentLikedEvent(
                                    $likeRecord->doc_id,
                                    $likeRecord->doc->created_by,
                                    $this->auth->user()->getId(),
                                    $likeRecord->doc->title,
                                    false // 取消点赞
                                )
                            );
                        }
                    }
                }

                $this->wikiLikeModel::query()->whereIn('like_id', $ids)->delete();

                DB::commit();
                return 1;
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /// wiki文档点赞 END
        //////////////////////////////////////////////


        //////////////////////////////////////////////
        /// wiki文档评论
        /**
         * wiki文档评论-获取列表
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */
        public function getWikiCommentList(array $filter = [], array $op = [], string $sort = 'comment_id', string $order = 'DESC', int $limit = 10)
        {
            $query = $this->wikiCommentModel::query();
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            $query = $query->with('user');

            // 获取所有评论
            $comments = $query->orderBy($sort, $order)->get()->toArray();
            $commentIds = array_column($comments, 'comment_id');

            // 获取所有回复（无论是否 parent_id 存在）
            $replies = $this->wikiReplyModel::query()
                ->with('user')
                ->whereIn('comment_id', $commentIds)
                ->orderBy('created_at')
                ->get()
                ->toArray();

            // 获取所有一级评论的用户（用于一级回复显示）
            $commentUserMap = [];
            foreach ($comments as $comment) {
                $commentUserMap[$comment['comment_id']] = $comment['user']['name'] ?? '';
            }

            // 获取所有回复 id => user_name 映射（用于二级回复）
            $replyUserMap = [];
            foreach ($replies as $reply) {
                $replyUserMap[$reply['reply_id']] = $reply['user']['name'] ?? '';
            }

            // 处理 replies，添加 reply_to_user_name 字段
            foreach ($replies as &$reply) {
                if ($reply['parent_id']) {
                    // 二级回复，查 parent reply 的用户名
                    $reply['reply_to_user_name'] = $replyUserMap[$reply['parent_id']] ?? '';
                } else {
                    // 一级回复，查评论的用户名
                    $reply['reply_to_user_name'] = $commentUserMap[$reply['comment_id']] ?? '';
                }
            }

            // 将回复挂到对应评论下
            $replyMap = [];
            unset($reply);
            foreach ($replies as $reply) {
                $replyMap[$reply['comment_id']][] = $reply;
            }

            // 整合返回结构
            foreach ($comments as &$comment) {
                $comment['replies'] = $replyMap[$comment['comment_id']] ?? [];
            }

            return [
                'code' => 1,
                'msg' => 'success',
                'data' => $comments,
            ];
        }

        /**
         * wiki文档评论-新增/编辑
         * @param int $comment_id
         * @param array $values
         * @return bool|Builder|Model|int
         */
        public function doWikiCommentEdit(int $comment_id, array $values)
        {
            // 正常写评论流程
            if ($comment_id > 0) {
                $row = $this->wikiCommentModel::query()->find($comment_id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }

                $result = $row->update($values);
            } else {
                $values['user_id'] = $this->auth->user()->getId();
                $result = $this->wikiCommentModel::query()->create($values);
            }

            return $result;
        }


        /**
         * wiki文档评论-删除
         * @param $ids
         * @return int
         */
        public function doWikiCommentDelete($ids): int
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);

                $this->wikiCommentModel::query()->whereIn('comment_id', $ids)->delete();

                DB::commit();
                return 1;
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * wiki文档评论-回复新增/编辑
         * @param int $reply_id
         * @param array $values
         * @return bool|Builder|Model|int
         */
        public function doWikiReplyEdit(int $reply_id, array $values)
        {
            if ($reply_id > 0) {
                $row = $this->wikiReplyModel::query()->find($reply_id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }

                $result = $row->update($values);
            } else {
                $values['user_id'] = $this->auth->user()->getId();
                $result = $this->wikiReplyModel::query()->create($values);
            }

            return $result;
        }

        /**
         * wiki文档评论-回复删除
         * @param $ids
         * @return int
         */
        public function doWikiReplyDelete($ids): int
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);

                $this->wikiReplyModel::query()->whereIn('reply_Id', $ids)->delete();

                DB::commit();
                return 1;
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /// wiki文档评论 END
        //////////////////////////////////////////////
        

        //////////////////////////////////////////////
        /// Wiki空间
        /**
         * Wiki空间-获取列表
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */
        public function getWikiSpaceList(array $filter = [], array $op = [], string $sort = 'space_id', string $order = 'DESC', int $limit = 10)
        {
            $user_id = $filter['user_id'] ?? $this->auth->user()->getId();
            unset($filter['user_id']);

            $authService = make(AuthService::class);
            $isSuper = $authService->isSuper($user_id, ['Director']);
            $joinedSpaceIds = $isSuper ? [] : $this->getJoinedSpaceIds($user_id);

            /** @var $query WikiSpaceModel */
            $query = $this->wikiSpaceModel::query();
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            $query = $query->withCount(['members', 'documents']);

            // ⭐关键改动：根据是否是超管，动态构建 where 条件
            if (!$isSuper) {
                $query->where(function ($q) use ($joinedSpaceIds) {
                    $q->whereIn('space_id', $joinedSpaceIds)
                        ->orWhere('is_public', 1);
                });
            }

            // 成员信息
            $query = $query->with(['members' => function ($q) use ($user_id) {
                $q->where('user_id', $user_id)->orWhereIn('group_id', $this->getJoinedGroupIds($user_id));
            }]);

            $result = $query->orderBy($sort, $order)->paginate($limit)->toArray();

            // 当前用户角色提取
            $rolePriority = BiWikiCode::ROLE_PRIORITY_EN;

            foreach ($result['data'] as &$item) {
                $currentRole = null;
                $maxPriority = -1;

                if (!empty($item['members']) && is_array($item['members'])) {
                    foreach ($item['members'] as $member) {
                        $role = $member['role'] ?? null;
                        if ($role && ($rolePriority[$role] ?? 0) > $maxPriority) {
                            $currentRole = $role;
                            $maxPriority = $rolePriority[$role];
                        }
                    }
                }

                $item['current_user_role'] = $currentRole;
            }

            return $result;
        }


        /**
         * Wiki空间-新增/编辑
         * @param int $space_id
         * @param array $values
         * @return bool|Builder|Model|int
         */
        public function doSpaceEdit(int $space_id, array $values)
        {
            if ($space_id > 0) {
                $row = $this->wikiSpaceModel::query()->find($space_id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }
                $result = $row->update($values);
            } else {
                $values['created_by'] = $this->auth->user()->getId();
                $result = $this->wikiSpaceModel::query()->create($values);
                $this->doSpaceMemberEdit(-1, ['space_id' => $result->space_id, 'associated_field' => 'user_id', 'user_id' => $this->auth->user()->getId(), 'role' => 'owner']);
            }

            return $result;
        }

        /**
         * Wiki空间-删除
         * @param $ids
         * @return int
         */
        public function doSpaceDelete($ids): int
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);

                // 查询并删除该空间下的所有文档
                $this->wikiDocumentModel::query()->whereIn('space_id', $ids)->delete();
                
                // 删除空间
                $this->wikiSpaceModel::query()->whereIn('space_id', $ids)->delete();

                DB::commit();
                return 1;
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        public function getJoinedSpaceIds(int $user_id): array
        {
            // 获取用户所在的所有群组 ID
            $groupData = $this->getUserGroupList([], [], 'group_id', 'DESC', 9999);
            $groupIds = array_column($groupData['data'] ?? [], 'group_id');

            // 查询成员记录：用户身份 或 群组身份
            $memberRecords = $this->wikiSpaceMemberModel::query()
                ->join('wiki_spaces as spaces', 'wiki_space_members.space_id', '=', 'spaces.space_id')
                ->whereNull('wiki_space_members.deleted_at') // 成员未被删除
                ->whereNull('spaces.deleted_at') // 空间未被删除
                ->where(function ($query) use ($user_id, $groupIds) {
                    $query->where(function ($q) use ($user_id) {
                        $q->where('associated_field', 'user_id')
                            ->where('user_id', $user_id);
                    });

                    if (!empty($groupIds)) {
                        $query->orWhere(function ($q) use ($groupIds) {
                            $q->where('associated_field', 'group_id')
                                ->whereIn('group_id', $groupIds);
                        });
                    }
                })
                ->pluck('wiki_space_members.space_id')  // 只取空间 ID
                ->unique()
                ->toArray();

            return $memberRecords;
        }


        /**
         * 根据空间ID获取当前用户角色
         * @param int $space_id
         * @return string|null
         */
        public function getUserRoleBySpaceId(int $space_id, int $user_id): ?string
        {

            $space = $this->wikiSpaceModel::query()->find($space_id);
            if (!$space) {
                return null;
            }

            $member = $space->members->where('user_id', $user_id)->first();
            if (!$member) {
                return null;
            }

            return $member->role;
        }

        /// Wiki空间 END
        //////////////////////////////////////////////


        //////////////////////////////////////////////
        /// 人员群组
        /**
         * 人员群组-获取列表
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */
        public function getUserGroupList(array $filter = [], array $op = [], string $sort = 'group_id', string $order = 'DESC', int $limit = 10)
        {
            $user_id =  $filter['user_id'] ?? $this->auth->user()->getId();
            unset($filter['user_id']);

            $authService = make(AuthService::class);
            $isSuper = $authService->isSuper($user_id, ['Director']);

            // 查询该用户在该群组中的角色是否满足条件
            $role = [];
            if (isset($filter['role']) && !empty($filter['role'])) {
                $role = $filter['role'];
                unset($filter['role']);
            }

            /** @var $query WikiUserGroupModel */
            $query = $this->wikiUserGroupModel::query();
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);

            // 仅普通用户过滤所在群组（超级管理员获取所有）
            if (!$isSuper) {
                if (!empty($role)) {
                    $query = $query->whereHas('users', function ($q) use ($user_id, $role) {
                        $q->where('user_id', $user_id)->whereIn('role', $role);
                    });
                } else {
                    $query = $query->whereHas('users', function ($q) use ($user_id) {
                        $q->where('user_id', $user_id);
                    });
                }
            }

            // 计算群组用户数量
            $query = $query->withCount('users');

            // 获取当前用户在该组下的映射信息（仅加载自己的）
            $query = $query->with(['users' => function ($q) use ($user_id) {
                $q->where('user_id', $user_id);
            }]);

            $result = $query->orderBy($sort, $order)->paginate($limit)->toArray();

            // 当前用户角色提取
            foreach ($result['data'] as &$item) {
                $item['current_user_role'] = $item['users'][0]['role'] ?? null;
                unset($item['users']); // 不再返回完整 users 映射信息
            }

            return $result;
        }

        /**
         * 人员群组-新增/编辑
         * @param int $group_id
         * @param array $values
         * @return bool|Builder|Model|int
         */
        public function doUserGroupEdit(int $group_id, array $values)
        {
            Db::beginTransaction();
            try {
                if ($group_id > 0) {
                    $row = $this->wikiUserGroupModel::query()->find($group_id);
                    if (!$row) {
                        throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                    }

                    $result = $row->update($values);
                } else {

                    $created_by = $this->auth->user()->getId();
                    $values['created_by'] = $created_by;
                    $result = $this->wikiUserGroupModel::query()->create($values);
                    $this->doGroupMemberEdit(-1, ['group_id' => $result->group_id, 'user_id' => $created_by, 'role' => 'owner']);
                }
                DB::commit();
                return $result;
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * 人员群组-删除
         * @param $ids
         * @return int
         */
        public function doUserGroupDelete($ids): int
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);

                $this->wikiUserGroupModel::query()->whereIn('group_id', $ids)->delete();

                DB::commit();
                return 1;
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        public function getJoinedGroupIds(int $user_id): array
        {
            // 获取用户所在的所有群组 ID
            $groupData = $this->getUserGroupList([], [], 'group_id', 'DESC', 9999);
            $groupIds = array_column($groupData['data'] ?? [], 'group_id');

            return $groupIds;
        }
        
        /// 人员群组 END
        //////////////////////////////////////////////

        
        //////////////////////////////////////////////
        /// 成员管理
        /** 
         * 目录成员管理-获取列表
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */
        public function getCatalogMemberList(array $filter = [], array $op = [], string $sort = 'catalog_id', string $order = 'DESC', int $limit = 10)
        {
            /** @var $query WikiCatalogMemberModel */
            $query = $this->wikiCatalogMemberModel::query();
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);

            $query = $query
                ->with(['user',
                    'group' => function ($q) {
                        $q->withCount('users');
                    }
                ]);

            return $query->orderBy($sort, $order)->paginate($limit)->toArray();
        }

        /**
         * 目录成员管理-新增/编辑
         * @param int $member_id
         * @param array $values
         * @return bool|Builder|Model|int
         */
        public function doCatalogMemberEdit(int $member_id, array $values)
        {
            if ($member_id > 0) {
                $row = $this->wikiCatalogMemberModel::query()->find($member_id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }
                $result = $row->update($values);
            } else {
                $result = $this->wikiCatalogMemberModel::query()->create($values);
            }

            return $result;
        }

        /**
         * 目录成员管理-批量新增
         * @param int $catalog_id
         * @param array $ids
         * @param string $associated_field
         * @return bool|Builder|Model|int
         */
        public function doCatalogMemberMultiAdd(int $catalog_id, array $ids, string $associated_field = 'user_id')
        {
            // 1.先获取该目录下已存在的用户ID
            $existingMembers = $this->wikiCatalogMemberModel::query()
            ->where('catalog_id', $catalog_id)
            ->pluck($associated_field)
            ->toArray();
            // 过滤掉已存在的用户ID
            $newMemberIds = array_diff($ids, $existingMembers);
            
            // 如果没有新用户需要添加，直接返回true
            if (empty($newMemberIds)) {
                return true;
            }

            $defaultRole = BiWikiCode::GET_ROLE_EN['成员'];
            $data = [];
            $now = date('Y-m-d H:i:s');
            foreach ($newMemberIds as $id) {
                $data[] = [
                    'catalog_id' => $catalog_id,
                    'associated_field' => $associated_field,
                    $associated_field => $id,
                    'role' => $defaultRole,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
            
            return $this->wikiCatalogMemberModel::query()->insert($data);
        }

        /**
         * 目录成员管理-删除
         * @param $ids
         * @return int
         */
        public function doCatalogMemberDelete($ids): int
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);
                
                $this->wikiCatalogMemberModel::query()->whereIn('member_id', $ids)->delete();

                DB::commit();
                return 1;
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * 目录成员管理-获取目录成员权限角色
         * @param int $id 要查询的id 可能为 user_id | group_id | department_id | system_role_id
         * @param int $catalog_id
         * 
         */
        public function getCatalogMemberRole(int $id, int $catalog_id, string $associated_field = 'user_id')
        {
            $userId = $associated_field === 'user_id'
                ? ($id ?? $this->auth->user()->getId())
                : $this->auth->user()->getId();

            // 1.获取当前成员所加入的群组
            $groupData = $this->getUserGroupList([], [], 'group_id', 'DESC', 99999);
            $groupData = $groupData['data'] ?? [];
            $groupIds = array_column($groupData,  'group_id');

            // 查询当前目录下所有关联的成员
            $allMember = $this->wikiCatalogMemberModel::query()
                ->where('catalog_id', $catalog_id)
                ->get()->toArray();
            $userMember = array_column($allMember, null,'user_id');
            $groupMember = array_column($allMember, null,'group_id');

            // 筛选 groupMember 中，group_id 存在于 groupIds 中的条目
            $matchedGroupMembers = array_filter($groupMember, function ($member) use ($groupIds) {
                return in_array($member['group_id'], $groupIds);
            });

            $asUserRole = $userMember[$userId]['role']?? null;

            $asGroupRoles = array_column($matchedGroupMembers, 'role', 'group_id'); // 作为群组在目录中的角色
            // 定义权限等级（数值越大权限越高）
            $rolePriority = BiWikiCode::ROLE_PRIORITY_EN;
            // 获取权限最高的群组角色
            $asGroupRole = null;
            foreach ($asGroupRoles as $role) {
                if ($asGroupRole === null || $rolePriority[$role] > $rolePriority[$asGroupRole]) {
                    $asGroupRole = $role;
                }
            }

            // 比较作为用户和作为群组的角色，返回权限更高的一个
            $finalRole = $asUserRole;
            if ($asGroupRole !== null && (
                    $finalRole === null || $rolePriority[$asGroupRole] > $rolePriority[$finalRole]
                )) {
                $finalRole = $asGroupRole;
            }

            return $finalRole;
        }

         
        /**
         * 空间成员管理-获取列表
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */

        /**
         * 空间成员管理-获取列表
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */
        public function getSpaceMemberList(array $filter = [], array $op = [], string $sort = 'member_id', string $order = 'DESC', int $limit = 10)
        {
            /** @var $query WikiSpaceMemberModel */
            $query = $this->wikiSpaceMemberModel::query();

            // 处理name字段的复合搜索
            if (isset($filter['name']) && !empty($filter['name'])) {
                $name = $filter['name'];
                $query->where(function ($q) use ($name) {
                    // 搜索用户名称
                    $q->whereHas('user', function ($subQuery) use ($name) {
                        $subQuery->where('name', 'like', "%{$name}%");
                    })
                        // 或搜索群组名称
                        ->orWhereHas('group', function ($subQuery) use ($name) {
                            $subQuery->where('group_name', 'like', "%{$name}%");
                        });
                });

                // 从filter中移除name参数，避免buildparams处理时出错
                unset($filter['name']);
                unset($op['name']);
            }

            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);

            $query = $query
                ->with(['user',
                    'group' => function ($q) {
                        $q->withCount('users');
                    }
                ]);

            return $query->orderBy($sort, $order)->paginate($limit)->toArray();
        }

        /**
         * 空间成员管理-新增/编辑
         * @param int $member_id
         * @param array $values
         * @return bool|Builder|Model|int
         */
        public function doSpaceMemberEdit(int $member_id, array $values)
        {
            if ($member_id > 0) {
                $row = $this->wikiSpaceMemberModel::query()->find($member_id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }

                $result = $row->update($values);
            } else {
                $result = $this->wikiSpaceMemberModel::query()->create($values);
            }

            return $result;
        }

        /**
         * 空间成员管理-批量新增
         * @param int $space_id
         * @param array $ids
         * @param string $associated_field
         * @return bool|Builder|Model|int
         */
        public function doSpaceMemberMultiAdd(int $space_id, array $ids, string $associated_field = 'user_id')
        {
             // 1.先获取该群组下已存在的用户ID
             $existingMembers = $this->wikiSpaceMemberModel::query()
             ->where('space_id', $space_id)
             ->pluck($associated_field)
             ->toArray();
            // 过滤掉已存在的用户ID
            $newMemberIds = array_diff($ids, $existingMembers);
            
            // 如果没有新用户需要添加，直接返回true
            if (empty($newMemberIds)) {
                return true;
            }

            $defaultRole = BiWikiCode::GET_ROLE_EN['成员'];
            $data = [];
            $now = date('Y-m-d H:i:s');
            foreach ($newMemberIds as $id) {
                $data[] = [
                    'space_id' => $space_id,
                    'associated_field' => $associated_field,
                    $associated_field => $id, 
                    'role' => $defaultRole,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            
            return $this->wikiSpaceMemberModel::query()->insert($data);
                
        }

        /**
         * 空间成员管理-删除
         * @param $ids
         * @return int
         */
        public function doSpaceMemberDelete($ids): int
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);

                $this->wikiSpaceMemberModel::query()->whereIn('member_id', $ids)->delete();

                DB::commit();
                return 1;
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * 空间成员管理-获取空间成员权限角色
         * @param int $id 要查询的id 可能为 user_id | group_id | department_id | system_role_id
         * @param int $space_id
         * @param string $type 要查询的类型 可能为 user_id | group_id | department_id | system_role_id
         * @return mixed
         */
        public function getSpaceMemberRole(int $id, int $space_id, string $associated_field = 'user_id')
        {
            $userId = $associated_field === 'user_id'
                ? ($id ?? $this->auth->user()->getId())
                : $this->auth->user()->getId();

            // 1.获取当前成员所加入的群组
            $groupData = $this->getUserGroupList([], [], 'group_id', 'DESC', 99999);
            $groupData = $groupData['data'] ?? [];
            $groupIds = array_column($groupData,  'group_id');

            // 查询当前空间下所有关联的成员
            $allMember = $this->wikiSpaceMemberModel::query()
                ->where('space_id', $space_id)
                ->get()->toArray();
            $userMember = array_column($allMember, null,'user_id');
            $groupMember = array_column($allMember, null,'group_id');

            // 筛选 groupMember 中，group_id 存在于 groupIds 中的条目
            $matchedGroupMembers = array_filter($groupMember, function ($member) use ($groupIds) {
                return in_array($member['group_id'], $groupIds);
            });

            $asUserRole = $userMember[$userId]['role']?? null;

            $asGroupRoles = array_column($matchedGroupMembers, 'role', 'group_id'); // 作为群组在空间中的角色
            // 定义权限等级（数值越大权限越高）
            $rolePriority = BiWikiCode::ROLE_PRIORITY_EN;
            // 获取权限最高的群组角色
            $asGroupRole = null;
            foreach ($asGroupRoles as $role) {
                if ($asGroupRole === null || $rolePriority[$role] > $rolePriority[$asGroupRole]) {
                    $asGroupRole = $role;
                }
            }

            // 比较作为用户和作为群组的角色，返回权限更高的一个
            $finalRole = $asUserRole;
            if ($asGroupRole !== null && (
                    $finalRole === null || $rolePriority[$asGroupRole] > $rolePriority[$finalRole]
                )) {
                $finalRole = $asGroupRole;
            }

            return $finalRole;

        }

        /**
         * 群组成员管理-获取列表
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */
        public function getGroupMemberList(array $filter = [], array $op = [], string $sort = 'mapping_id', string $order = 'DESC', int $limit = 10)
        {
            /** @var $query WikiUserGroupMappingModel */
            $query = $this->wikiUserGroupMappingModel::query();

            // 处理用户名搜索
            if (isset($filter['name']) && !empty($filter['name'])) {
                $query->whereHas('user', function ($q) use ($filter) {
                    $q->where('name', 'like', '%' . $filter['name'] . '%');
                });
                // 移除name过滤条件，避免直接查询mapping表
                unset($filter['name']);
            }

            // 处理其他查询条件
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);


            // 加载关联数据
            $query = $query->with(['user', 'userGroup']);

            return $query->orderBy($sort, $order)->paginate($limit)->toArray();
        }

        /**
         * 群组成员管理-新增/编辑
         * @param int $mapping_id
         * @param array $values
         * @return bool|Builder|Model|int
         */
        public function doGroupMemberEdit(int $mapping_id, array $values)
        {
            if ($mapping_id > 0) {
                $row = $this->wikiUserGroupMappingModel::query()->find($mapping_id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }

                $result = $row->update($values);
            } else {
                $values['role'] = $values['role'] ?? BiWikiCode::GET_ROLE_EN['成员'];
                $result = $this->wikiUserGroupMappingModel::query()->create($values);
            }

            return $result;
        }

        /**
         * 群组成员管理-批量添加
         * @param int $group_id
         * @param array $values
         * @return bool|Builder|Model|int
         */
        public function doGroupMemberMulti(int $group_id, array $user_ids)
        {
            // 先获取该群组下已存在的用户ID
            $existingUsers = $this->wikiUserGroupMappingModel::query()
                ->where('group_id', $group_id)
                ->pluck('user_id')
                ->toArray();

            // 过滤掉已存在的用户ID
            $newUserIds = array_diff($user_ids, $existingUsers);
            
            // 如果没有新用户需要添加，直接返回true
            if (empty($newUserIds)) {
                return true;
            }

            $defaultRole = BiWikiCode::GET_ROLE_EN['成员'];
            $data = [];
            $now = date('Y-m-d H:i:s');
            foreach ($newUserIds as $user_id) {
                $data[] = [
                    'group_id' => $group_id, 
                    'user_id' => $user_id, 
                    'role' => $defaultRole,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            return $this->wikiUserGroupMappingModel::query()->insert($data);
        }

        /**
         * 群组成员管理-删除
         * @param $ids
         * @return int
         */
        public function doGroupMemberDelete($ids): int
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);

                $this->wikiUserGroupMappingModel::query()->whereIn('mapping_id', $ids)->delete();

                DB::commit();
                return 1;
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * 群组成员管理-获取群组成员权限角色
         * @param int $mapping_id
         * @return mixed
         */
        public function getGroupMemberRole(int $user_id, int $group_id)
        {
            $user_group = $this->wikiUserGroupMappingModel::query()
                ->where('user_id', $user_id)
                ->where('group_id', $group_id)
                ->first();
            if ($user_group) {
                return $user_group->role;
            }
        }
        /// 成员管理 END
        //////////////////////////////////////////////

        //////////////////////////////////////////////
        /// 历史记录
        /**
         * 获取指定文档的所有历史版本
         * @param int $doc_id 文档ID
         * @return array 历史版本列表
         */
        public function getWikiHistoryList(int $doc_id)
        {
            if ($doc_id <= 0) {
                throw new AppException(StatusCode::ERR_SERVER, '文档ID无效');
            }
            
            // 首先检查文档是否存在
            $document = $this->wikiDocumentModel::query()->find($doc_id);
            if (!$document) {
                throw new AppException(StatusCode::ERR_SERVER, '文档不存在');
            }
            
            // 查询该文档的所有历史版本，按版本号降序排列
            $versions = $this->wikiDocumentVersionModel::query()
                ->where('doc_id', $doc_id)
                ->with(['creator' => function ($q) {
                    $q->select('id', 'name', 'avatar');
                }])
                ->orderBy('version_number', 'DESC')
                ->get()
                ->toArray();
            
            //添加文档标题
            foreach ($versions as $key => $version) {
                $versions[$key]['title'] = $document->title;
            }

            return $versions;
        }

        /**
         * 恢复文档到指定的历史版本
         * @param int $version_id 要恢复的版本ID
         * @return array 恢复结果
         */
        public function restoreWikiVersion(int $version_id)
        {
            if ($version_id <= 0) {
                throw new AppException(StatusCode::ERR_SERVER, '版本ID无效');
            }
            
            // 获取要恢复的版本
            $version = $this->wikiDocumentVersionModel::query()->find($version_id);
            if (!$version) {
                throw new AppException(StatusCode::ERR_SERVER, '历史版本不存在');
            }
            
            // 获取对应的文档
            $document = $this->wikiDocumentModel::query()->find($version->doc_id);
            if (!$document) {
                throw new AppException(StatusCode::ERR_SERVER, '文档不存在');
            }
            
            // 开启事务
            DB::beginTransaction();
            try {
                // 获取当前最大版本号
                $currentVersionNumber = $this->wikiDocumentVersionModel::query()
                    ->where('doc_id', $document->doc_id)
                    ->max('version_number');
                
                $newVersionNumber = $currentVersionNumber ? $currentVersionNumber + 1 : 1;
                
                // 复制历史版本记录，创建为新的版本记录
                $this->wikiDocumentVersionModel::query()->create([
                    'doc_id' => $document->doc_id,
                    'version_number' => $newVersionNumber,
                    'content_html' => $version->content_html,
                    'content_markdown' => $version->content_markdown,
                    'editor_type' => $version->editor_type,
                    'created_by' => $this->auth->user()->getId(),
                    'change_log' => $this->auth->user()->name . ' 恢复自版本' . $version->version_number . '的内容'
                ]);
                
                // 3. 更新文档内容为要恢复的历史版本内容
                $document->update([
                    'content_html' => $version->content_html,
                    'content_markdown' => $version->content_markdown,
                    'current_editor_type' => $version->editor_type,
                    'updated_by' => $this->auth->user()->getId(),
                    'updated_at' => Carbon::now()
                ]);
                
                DB::commit();
                
                return [
                    'success' => true,
                    'doc_id' => $document->doc_id,
                    'new_version' => $newVersionNumber,
                    'restored_from_version' => $version->version_number,
                    'message' => '文档已恢复到版本 ' . $version->version_number . '的内容'
                ];
            } catch (Exception $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, '恢复版本失败: ' . $e->getMessage());
            }
        }

        /// 历史记录 END
        //////////////////////////////////////////////


        /**
         * 添加文档权限过滤条件（包含空间、目录、文档三层权限）
         * @param $query
         * @param int|null $userId
         * @return void
         */
        private function addDocumentPermissionFilter($query, int $userId = null)
        {
            if ($userId === null) {
                $userId = $this->auth->user()->getId();
            }
            
            $authService = make(AuthService::class);
            $isSuper = $authService->isSuper($userId, ['Director']);
            
            if ($isSuper) {
                return; // 超级管理员可以访问所有文档
            }

            // 获取用户可访问的空间ID
            $accessibleSpaceIds = $this->getAccessibleSpaceIds($userId);
            
            if (empty($accessibleSpaceIds)) {
                // 如果用户没有可访问的空间，返回空结果
                $query->whereRaw('1 = 0'); // 没有可访问空间时返回空
                return;
            }
            
            // 限制在用户可访问的空间内
            $query->whereIn('space_id', $accessibleSpaceIds);
            
            // 获取用户加入的空间ID（区别于仅公开可见的空间）
            $joinedSpaceIds = $this->getJoinedSpaceIds($userId);
            
            // 获取用户可访问的目录ID（已经包含了目录权限逻辑）
            $accessibleCatalogIds = $this->getAccessibleCatalogIds($userId);
            array_unshift($accessibleCatalogIds, "0"); // 添加 "0" 到最前面,根目录下的文档始终可见

            
            if (!empty($accessibleCatalogIds)) {
                // 限制只能访问有权限的目录
                $query->whereIn('catalog_id', $accessibleCatalogIds);
            }
            
            // 应用文档级权限过滤
            $query->where(function($mainQuery) use ($userId, $joinedSpaceIds) {
                // 情况1：公开文档
                $mainQuery->where('is_public', 1);
                
                // 情况2：组内共享文档 + 用户是空间成员
                if (!empty($joinedSpaceIds)) {
                    $mainQuery->orWhere(function($subQuery) use ($joinedSpaceIds) {
                        $subQuery->where('is_public', 0)
                            ->whereIn('space_id', $joinedSpaceIds);
                    });
                }
                
                // 情况3：私有文档，用户是创建者
                $mainQuery->orWhere(function($subQuery) use ($userId) {
                    $subQuery->where('is_public', -1)
                        ->where('wiki_documents.created_by', $userId);
                });
            });
        }

        /**
         * 检查文档编辑权限
         * @param $document
         * @param int $userId
         * @return void
         * @throws AppException
         */
        private function checkDocumentEditPermission($document, int $userId)
        {
            $authService = make(AuthService::class);
            $isSuper = $authService->isSuper($userId, ['Director']);
            
            if ($isSuper) {
                return; // 超级管理员可以编辑所有文档
            }
            
            // 检查是否是文档创建者
            if ($document->created_by == $userId) {
                return; // 创建者可以编辑自己的文档
            }
            
            // 检查空间权限
            $userRole = $this->getUserRoleBySpaceId($document->space_id, $userId);
            if (!empty($userRole)) {
                // 检查文档权限级别
                if ($document->is_public == 1) {
                    return; // 公开文档，空间成员可以编辑
                }
                
                if ($document->is_public == 0) {
                    // 组内共享文档，需要是空间成员
                    return;
                }
            }
            
            // 私有文档只有创建者可以编辑
            if ($document->is_public == -1 && $document->created_by != $userId) {
                throw new AppException(StatusCode::ERR_SERVER, '您没有权限编辑此文档');
            }
            
            // 其他情况都没有权限
            throw new AppException(StatusCode::ERR_SERVER, '您没有权限编辑此文档');
        }

        /**
         * 检查文档访问权限
         * @param $document
         * @param int $userId
         * @return bool
         */
        private function checkDocumentAccessPermission($document, int $userId): bool
        {
            $authService = make(AuthService::class);
            $isSuper = $authService->isSuper($userId, ['Director']);
            
            if ($isSuper) {
                return true; // 超级管理员可以访问所有文档
            }
            
            // 检查是否是文档创建者
            if ($document->created_by == $userId) {
                return true; // 创建者可以访问自己的文档
            }
            
            // 公开文档所有人可以访问
            if ($document->is_public == 1) {
                return true;
            }
            
            // 组内共享文档，需要是空间成员
            if ($document->is_public == 0) {
                $userRole = $this->getUserRoleBySpaceId($document->space_id, $userId);
                return !empty($userRole);
            }
            
            // 私有文档只有创建者可以访问
            if ($document->is_public == -1) {
                return $document->created_by == $userId;
            }
            
            return false;
        }

        //////////////////////////////////////////////
        /// 文档标签

        /**
         * 文档标签-获取文档标签列表
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */
        public function getDocTagList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
        {
            /** @var $query WikiDocumentTagModel */
            $query = $this->wikiDocumentTagModel::query();
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit,$query);

            $query = $query->with(['tag']);

            return $query->orderBy($sort, $order)->paginate($limit);
        }

        /**
         * 文档标签-新增/编辑
         * @param int $id
         * @param array $values
         * @return bool|Builder|Model|int
         */
        public function doDocTagEdit(int $id, array $values)
        {
            if ($id > 0) {
                $row = $this->wikiDocumentTagModel::query()->find($id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }
                $row->update($values);
                return $row;
            } else {
                $values['created_by'] = $this->auth->user()->getId();
                $result = $this->wikiDocumentTagModel::query()->create($values);
                return $result;
            }
        }

        /**
         * 文档标签-删除
         * @param $ids
         * @return int
         */
        public function doDocTagDelete($ids): int
        {
            DB::beginTransaction();
            try {
                $ids = is_string($ids) ? explode(',', $ids) : (is_array($ids) ? $ids : [$ids]);

                $this->wikiDocumentTagModel::query()->whereIn('id', $ids)->delete();

                DB::commit();
                return 1;
            } catch (AppException $e) {
                DB::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /// 文档标签 END
        //////////////////////////////////////////////
    

        /**
         * 获取用户可访问的所有空间ID
         * 
         * @param int|null $userId 用户ID，默认为当前用户
         * @return array 可访问的空间ID数组
         */
        protected function getAccessibleSpaceIds(int $userId = null): array
        {
            // 如果未提供用户ID，则使用当前登录用户
            if ($userId === null) {
                $currentUser = $this->auth->user();
                $userId = $currentUser->getId();
            }
            
            // 检查是否超级管理员
            $authService = make(AuthService::class);
            $isSuper = $authService->isSuper($userId, ['Director']);
            
            // 获取用户可访问的空间ID
            if (!$isSuper) {
                // 普通用户: 获取加入的空间 + 公开空间
                $joinedSpaceIds = $this->getJoinedSpaceIds($userId);
                $publicSpaceIds = $this->wikiSpaceModel::query()
                    ->where('is_public', 1)
                    ->pluck('space_id')
                    ->toArray();
                
                // 合并并去重
                return array_values(array_unique(array_merge($joinedSpaceIds, $publicSpaceIds)));
            } else {
                // 超级管理员: 可访问所有空间
                return $this->wikiSpaceModel::query()
                    ->pluck('space_id')
                    ->toArray();
            }
        }

        protected function getPublicSpaceIds(): array
        {
            return $this->wikiSpaceModel::query()
                ->where('is_public', 1)
                ->pluck('space_id')
                ->toArray();
        }


        /**
         * 获取用户可访问的所有目录ID
         *
         * @param int|null $userId 用户ID，默认为当前用户
         * @return array 目录ID数组
         */
        protected function getAccessibleCatalogIds( int $userId = null): array
        {
            // 如果未提供用户ID，则使用当前登录用户
            if ($userId === null) {
                $userId = $this->auth->user()->getId();
            }
            
            // 检查是否超级管理员
            $authService = make(AuthService::class);
            $isSuper = $authService->isSuper($userId, ['Director']);
            $spaceIds = $this->getAccessibleSpaceIds($userId);
            $publicSpaceIds = $this->getPublicSpaceIds();
            //$isSuper = false;

            // 获取用户所加入的空间（用于区分共享目录的权限）
            $joinedSpaceIds = $spaceIds;
            $joinedSpaceIdsButNotPublic = array_diff($joinedSpaceIds, $publicSpaceIds);

            // 查询目录构建器
            $query = $this->wikiCatalogModel::query();
            
            // 限制只能访问指定空间下的目录
            $query->whereIn('space_id', $spaceIds);
            
            // 根据用户权限应用目录访问规则
            if ($isSuper) {
                // 超级管理员可以访问所有目录，包括私有目录
                // 不需要额外限制条件 跳过
            } else {
                // 普通用户需要根据规则限制：
                // 1. 公开目录(is_public=1)对所有人可见
                // 2. 公开空间，并且是其中成员的
                // 3. 空间内共享目录(is_public=0)对空间成员可见
                // 4. 私有目录(is_public=-1)仅对创建者可见
                $query->where(function($q) use ($userId, $joinedSpaceIds, $joinedSpaceIdsButNotPublic) {
                    // 公开目录任何人可见
                    $q->where('is_public', 1);

                    // 公开空间，并且是其中成员的
                    $q->orWhere(function($subq) use ($joinedSpaceIds) {
                        $subq->where('is_public', 0)
                            ->whereIn('space_id', $joinedSpaceIds);
                    });
                    
                    // 空间内共享目录，仅空间成员可见
                    if (!empty($joinedSpaceIdsButNotPublic)) {
                        $q->orWhere(function($subq) use ($joinedSpaceIdsButNotPublic) {
                            $subq->where('is_public', 0)
                                ->whereIn('space_id', $joinedSpaceIdsButNotPublic);
                        });
                    }
                    
                    // 私有目录，仅创建者可见
                    $q->orWhere(function($subq) use ($userId) {
                        $subq->where('is_public', -1)
                            ->where('created_by', $userId);
                    });
                });
            }
            
            // 执行查询并返回目录ID数组
            return $query->pluck('catalog_id')->toArray();
        }

    }
